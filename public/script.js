// Initialize Firebase
const firebaseConfig = {
    apiKey: "AIzaSyC2Ayt2MULsUIs2Z-bgWGwLNIS6rNkCCjI",
    authDomain: "movie-map-cb98d.firebaseapp.com",
    projectId: "movie-map-cb98d",
    storageBucket: "movie-map-cb98d.appspot.com",
    messagingSenderId: "15298927968",
    appId: "1:15298927968:web:f805150aefc78ce6c11205",
    measurementId: "G-QE9125KGZE"
};

firebase.initializeApp(firebaseConfig);
const db = firebase.firestore();

const emailInput = document.getElementById('email');
const submitButton = document.getElementById('submitButton');
const messageDiv = document.getElementById('message');

submitButton.addEventListener('click', async () => {
    const email = emailInput.value;
    
    if (!email.includes('@')) {
        messageDiv.className = 'message error';
        messageDiv.textContent = 'Please enter a valid email address';
        return;
    }

    try {
        await db.collection('deletionRequests').add({
            email: email,
            timestamp: firebase.firestore.FieldValue.serverTimestamp()
        });
        
        emailInput.disabled = true;
        submitButton.disabled = true;
        
        messageDiv.className = 'message success';
        messageDiv.textContent = 'Your account deletion request has been received. We will process it shortly. We will send you the result.';
        
    } catch (error) {
        messageDiv.className = 'message error';
        messageDiv.textContent = 'Error submitting request. Please try again later.';
    }
}); 