<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Account - Movie Map</title>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 500px;
            width: 100%;
            padding: 40px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0 0 20px;
            color: #333;
        }
        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .message.success {
            display: block;
            background-color: #d4edda;
            color: #155724;
        }
        .message.error {
            display: block;
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Deleting account</h1>
        <p>Deleting your Movie Map account will remove your data from Movie Map. This cannot be undone.</p>
        <input type="email" id="email" placeholder="Enter your email" required>
        <button id="submitButton" class="delete-btn">Submit Request</button>
        <div id="message" class="message"></div>
    </div>
    <script type="module" src="script.js"></script>
</body>
</html> 