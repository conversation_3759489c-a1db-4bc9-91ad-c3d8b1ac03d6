const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });
const express = require('express');
const app = express();
const port = 3000;



// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Import routes
const botRoutes = require('./routes/botRoutes');
const battleRoutes = require('./routes/battlesRoutes');

// Use routes
app.use('/api', botRoutes);
app.use('/api', battleRoutes);

// Start server
app.listen(port, () => {
    console.log(`Server is running on http://localhost:${port}`);
}); 