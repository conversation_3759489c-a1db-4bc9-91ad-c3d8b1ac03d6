const express = require("express");
const router = express.Router();
const {
  collection,
  query,
  where,
  getDocs,
  deleteDoc,
  addDoc,
} = require("firebase/firestore");
const { db } = require("../config/firebase");
const OpenAI = require("openai");

if (!process.env.OPENAI_API_KEY) {
    throw new Error('OPENAI_API_KEY environment variable is missing');
}

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

// Artık db'yi doğrudan kullanabiliriz
async function deleteBotUsers() {
  try {
    console.log("Starting to delete bot users...");

    const usersRef = collection(db, "users");
    const q = query(usersRef, where("isBot", "==", true));
    const querySnapshot = await getDocs(q);

    let deleteCount = 0;

    for (const doc of querySnapshot.docs) {
      await deleteDoc(doc.ref);
      deleteCount++;
      console.log(`Deleted bot user with ID: ${doc.id}`);
    }

    return {
      success: true,
      message: `Successfully deleted ${deleteCount} bot users`,
    };
  } catch (error) {
    console.error("Error deleting bot users:", error);
    return { success: false, error: error.message };
  }
}

async function generateAIUsername() {
  const prompt = `Generate a single username based on realistic American first names and surnames. 
  The username should be a slightly modified version of common names, optionally include special characters like '_', '.', or numbers. Be 3 to 12 characters long.
  Examples:
  - John_Doe92
  - Sarah.Miller
  - Mike_Brown
  - Anna_Smith23
  Only return the username without any additional text or context.
`;

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "user", content: prompt },
      ],
      temperature: 1,
      max_tokens: 4,
    });

    let username = completion.choices[0].message.content.trim();
    return username;
  } catch (error) {
    console.error("Error generating username:", error);
    throw new Error("Failed to generate username.");
  }
}

async function createBotUsers(count = 1) {
  try {
    console.log(`Starting to create ${count} bot users...`);

    const usersRef = collection(db, "users");
    const createdBots = [];

    for (let i = 0; i < count; i++) {
      const username = await generateAIUsername();

      // Only create bot if we have a valid username
      if (username) {
        const botUser = {
          invitationCodes: [],
          level: 1,
          markerLocations: [],
          money: Math.floor(Math.random() * 1000),
          movieCollection: [],
          popcornSend: 0,
          profileDescription: "I am a movie enthusiast! 🎬",
          scanLocations: null,
          seenBattles: [],
          username: username,
          watchLater: null,
          isBot: true,
          createdAt: new Date().toISOString(),
        };

        const docRef = await addDoc(usersRef, botUser);
        createdBots.push({ id: docRef.id, ...botUser });
      }
    }

    return {
      success: true,
      message: `Successfully created ${createdBots.length} bot users`,
      bots: createdBots,
    };
  } catch (error) {
    console.error("Error creating bot users:", error);
    return { success: false, error: error.message };
  }
}

// Test route ekleyelim
router.get("/test", (req, res) => {
  res.json({ message: "Bot routes are working" });
});

// Status route
router.get("/status", (req, res) => {
  res.json({ status: "online" });
});

// Delete bots route
router.delete("/delete-bots", async (req, res) => {
  const result = await deleteBotUsers();
  if (result.success) {
    res.json(result);
  } else {
    res.status(500).json(result);
  }
});

// Yeni route: Bot oluşturma
router.post("/create-bots", async (req, res) => {
  const { count = 1 } = req.body;

  if (count < 1 || count > 100) {
    return res.status(400).json({
      success: false,
      error: "Count must be between 1 and 100",
    });
  }

  const result = await createBotUsers(count);
  if (result.success) {
    res.json(result);
  } else {
    res.status(500).json(result);
  }
});

// Mevcut botları listeleme endpoint'i
router.get("/list-bots", async (req, res) => {
  try {
    const usersRef = collection(db, "users");
    const q = query(usersRef, where("isBot", "==", true));
    const botsSnapshot = await getDocs(q);
    
    const bots = [];
    botsSnapshot.forEach((doc) => {
      bots.push({
        id: doc.id,
        ...doc.data(),
      });
    });
    res.json(bots);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
