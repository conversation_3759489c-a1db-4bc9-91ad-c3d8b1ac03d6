const express = require('express');
const router = express.Router();
const movieService = require('../services/movieService');
const { db } = require('../config/firebase');
const { 
  collection, 
  addDoc, 
  query, 
  getDocs, 
  where,
  deleteDoc
} = require('firebase/firestore');


// Create new battles
router.post("/create-battle", async (req, res) => {
    try {
        const { theme, count = 1 } = req.body;
        
        if (!theme) {
            return res.status(400).json({ error: "Theme is required." });
        }

        // Get all users
        const usersRef = collection(db, "users");
        const usersSnapshot = await getDocs(usersRef);
        
        // Get battles from last 24 hours
        const battlesRef = collection(db, "battles");
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const battlesSnapshot = await getDocs(
            query(battlesRef, where("createdAt", ">=", oneDayAgo))
        );

        // Get users who had battles in last 24 hours
        const recentlyBattledUsers = new Set();
        battlesSnapshot.forEach(doc => {
            const battle = doc.data();
            battle.opponents.forEach(opponent => {
                recentlyBattledUsers.add(opponent.userId);
            });
        });

        // Filter eligible users
        const eligibleUsers = [];
        usersSnapshot.forEach(doc => {
            const userId = doc.id;
            if (!recentlyBattledUsers.has(userId)) {
                eligibleUsers.push(userId);
            }
        });

        // Check if we have enough eligible users
        if (eligibleUsers.length < 2) {
            return res.status(400).json({ 
                error: "Not enough eligible users for battle" 
            });
        }

        const getRandomVoteCount = () => Math.floor(Math.random() * 100) + 1;

        const createdBattles = [];

        // Create specified number of battles
        for (let i = 0; i < count; i++) {
            const shuffledUsers = eligibleUsers.sort(() => 0.5 - Math.random());
            const selectedUsers = shuffledUsers.slice(0, 2);

            const movies = await movieService.getRandomMovies(2);

            const battleData = {
                theme,
                opponents: [
                    {
                        movieId: movies[0].id,
                        userId: selectedUsers[0],
                        voteCount: getRandomVoteCount()
                    },
                    {
                        movieId: movies[1].id,
                        userId: selectedUsers[1],
                        voteCount: getRandomVoteCount()
                    }
                ],
                createdAt: new Date(),
                isBot: true,
            };

            const docRef = await addDoc(battlesRef, battleData);
            createdBattles.push({
                battleId: docRef.id,
                battle: battleData
            });
        }

        res.status(201).json({
            battles: createdBattles
        });

    } catch (error) {
        console.error("Error creating battles:", error);
        res.status(500).json({ error: error.message });
    }
});

// Get specific movie details
// TODO: Is this route necessary?
router.get('/movies/:id', async (req, res) => {
    try {
        const movieId = req.params.id;
        const movieDetails = await movieService.getMovieDetails(movieId);
        res.json(movieDetails);
    } catch (error) {
        console.error('Movie details error:', error);
        res.status(500).json({ error: 'Failed to fetch movie details' });
    }
});

// Add new route to delete all bot battles
router.delete("/delete-all-bot-battles", async (req, res) => {
  try {
    console.log("Starting to delete bot battles...");

    const battlesRef = collection(db, "battles");
    const q = query(battlesRef, where("isBot", "==", true));
    const botBattlesSnapshot = await getDocs(q);
    
    let deleteCount = 0;

    for (const doc of botBattlesSnapshot.docs) {
      await deleteDoc(doc.ref);
      deleteCount++;
      console.log(`Deleted bot battle with ID: ${doc.id}`);
    }

    const result = {
      success: true,
      message: `Successfully deleted ${deleteCount} bot battles`,
      deletedCount: deleteCount
    };

    console.log(result.message);
    res.json(result);

  } catch (error) {
    console.error("Error deleting bot battles:", error);
    res.status(500).json({ 
      success: false, 
      error: error.message,
      message: "Error occurred while deleting bot battles"
    });
  }
});

module.exports = router;
