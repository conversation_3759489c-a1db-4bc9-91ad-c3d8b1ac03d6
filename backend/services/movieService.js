const axios = require('axios');
const config = require('../config/tmdb');

class MovieService {
    constructor() {
        this.apiKey = process.env.TMDB_API_KEY;
        this.baseUrl = 'https://api.themoviedb.org/3';
    }

    async getRandomMovies(count = 2) {
        try {
            const randomPage = Math.floor(Math.random() * 500) + 1;
            
            const sortOptions = [
                'vote_average.desc',
                'vote_average.asc',
                'primary_release_date.desc',
                'primary_release_date.asc',
                'revenue.desc',
                'revenue.asc'
            ];
            const randomSort = sortOptions[Math.floor(Math.random() * sortOptions.length)];

            const response = await axios.get(`${this.baseUrl}/discover/movie`, {
                params: {
                    api_key: this.apiKey,
                    language: 'en-US',
                    page: randomPage,
                    sort_by: randomSort,
                    'vote_count.gte': 100,
                    include_adult: false
                }
            });

            if (!response.data || !response.data.results) {
                throw new Error('Invalid response data from TMDB API');
            }

            const movies = response.data.results
                .filter(movie => movie && movie.id && movie.title)
                .sort(() => 0.5 - Math.random())
                .slice(0, count)
                .map(movie => ({
                    id: movie.id.toString(),
                    title: movie.title,
                }));

            console.log(movies);

            return movies;

        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    async getMovieDetails(movieId) {
        try {
            const response = await axios.get(`${this.baseUrl}/movie/${movieId}`, {
                params: {
                    api_key: this.apiKey,
                    language: 'en-US'
                }
            });
            
            return response.data;
        } catch (error) {
            console.error(`Error fetching movie details for ID ${movieId}:`, error);
            throw new Error('Failed to fetch movie details');
        }
    }
}

module.exports = new MovieService(); 