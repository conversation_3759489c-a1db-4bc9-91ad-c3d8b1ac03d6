import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/feature/battle/online_battle_lobby.dart';

class JoinBattleButton extends StatelessWidget {
  const JoinBattleButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => const OnlineBattleLobbyView(),
            ),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.blue,
          ),
          padding: const EdgeInsets.all(6),
          child: const Text(
            'Join Battle',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}

final class CircleVoteComponent extends StatelessWidget {
  final String username;
  final Map<String, dynamic> opponent;
  final VoidCallback callback;
  final Color radiusColor;

  const CircleVoteComponent({
    super.key,
    required this.username,
    required this.opponent,
    required this.callback,
    required this.radiusColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: radiusColor,
            blurRadius: 16,
            spreadRadius: 6,
          ),
        ],
      ),
      child: GestureDetector(
        onTap: callback,
        child: CircleAvatar(
          radius: 28,
          backgroundColor: Colors.grey.shade800,
          backgroundImage: opponent['user']['profilePhoto'] != null
              ? CachedNetworkImageProvider(
                  opponent['user']['profilePhoto'],
                )
              : null,
          child: opponent['user']['profilePhoto'] == null
              ? Text(
                  username.substring(0, 1),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                  ),
                )
              : null,
        ),
      ),
    );
  }
}
