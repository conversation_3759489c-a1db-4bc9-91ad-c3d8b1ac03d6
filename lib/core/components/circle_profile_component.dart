import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:movie_map/feature/onboard/profile%20photo/profile_photo_page.dart';
import 'package:movie_map/product/theme/theme.dart';

class CircleProfileComponent extends StatefulWidget {
  final String? letter;
  final bool? isEditingAvailable;
  final File? file;

  const CircleProfileComponent({
    super.key,
    this.letter,
    this.isEditingAvailable = false,
    this.file,
  });

  @override
  State<CircleProfileComponent> createState() => _CircleProfileComponentState();
}

class _CircleProfileComponentState extends State<CircleProfileComponent> {
  String? url;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _getImage();
  }

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? const CircularProgressIndicator()
        : Stack(
            clipBehavior: Clip.none,
            children: [
              Container(
                padding: const EdgeInsets.all(4.0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.blue,
                    width: 1.0,
                  ),
                ),
                child: CircleAvatar(
                  radius: 40,
                  backgroundColor: AppTheme.surface,
                  backgroundImage: widget.file != null
                      ? FileImage(
                          widget.file!,
                        )
                      : url != null
                          ? CachedNetworkImageProvider(
                              url!,
                            ) // TODO: cached network image providerın işe yaradığına emin değilim.
                          : null,
                  child: url == null && widget.file == null
                      ? Text(
                          widget.letter!,
                          style: const TextStyle(
                            fontSize: 36,
                            color: Colors.white,
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ),
              if (widget.isEditingAvailable!)
                Positioned(
                  left: -8,
                  top: -8,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ProfilePhotoPage(),
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(12.0),
                      color: Colors.transparent,
                      child: Container(
                        padding: const EdgeInsets.all(4.0),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.grey.shade800,
                        ),
                        child: const Icon(
                          Icons.edit,
                          color: Colors.blue,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          );
  }

  // get image from firebase storage
  Future<void> _getImage() async {
    final storageRef = FirebaseStorage.instance.ref();
    try {
      isLoading = true;
      String filePath;

      final User? user = FirebaseAuth.instance.currentUser;
      final FirebaseFirestore firestore = FirebaseFirestore.instance;
      final userId = user!.uid;
      filePath = 'ProfilePhoto/pp$userId';

      debugPrint(userId);

      final userProfilePhoto = firestore.collection('users').doc(userId).get();
      final data = (await userProfilePhoto).data();
      if (data != null && data['profilePhoto'] != null) {
        url = await storageRef.child(filePath).getDownloadURL();
        debugPrint('data is not null.');
      }
      debugPrint('Profile photo URL retrieved successfully and the URL is: $url');

      setState(() {});
    } on PlatformException catch (e) {
      // Check if the error is specifically an object-not-found error
      if (e.code == 'object-not-found') {
        debugPrint('No profile photo found for the user');
      } else {
        debugPrint('Failed to get image: ${e.message}');
      }
    } catch (e) {
      // Handle any other exceptions
      debugPrint('Unexpected error: $e');
    } finally {
      isLoading = false;
    }
  }
}
