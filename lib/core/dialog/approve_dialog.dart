import 'package:flutter/material.dart';
import 'package:movie_map/product/theme/theme.dart';

class ApproveDialog extends StatefulWidget {
  final String? title;
  final String contentText;
  final Color? buttonColor;
  final bool withTimer;
  final Function? onAccept;
  final String? acceptButtonText;

  const ApproveDialog({
    super.key,
    this.title,
    required this.contentText,
    this.buttonColor = AppTheme.red,
    this.withTimer = false,
    this.onAccept,
    this.acceptButtonText,
  });

  @override
  State<ApproveDialog> createState() => _ApproveDialogState();
}

class _ApproveDialogState extends State<ApproveDialog> {
  late String acceptButtonText = widget.acceptButtonText ?? 'Approve';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title ?? 'Warning'),
      content: Text(widget.contentText),
      shape: RoundedRectangleBorder(
        side: const BorderSide(
          color: AppTheme.grey,
        ),
        borderRadius: AppTheme.borderRadiusAll,
      ),
      actions: [
        if (widget.onAccept == null) ...[
          TextButton(
            style: TextButton.styleFrom(
              backgroundColor: widget.buttonColor,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Confirm'),
          ),
        ] else ...[
          TextButton(
            style: TextButton.styleFrom(
              backgroundColor: AppTheme.transparent,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);

              if (widget.onAccept != null) {
                widget.onAccept!();
              }
            },
            child: Text(
              acceptButtonText,
              style: const TextStyle(
                color: AppTheme.white,
              ),
            ),
          )
        ]
      ],
    );
  }
}
