import 'package:flutter/material.dart';

class CostumButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String text;
  final Color? backgroundColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? textColor;
  final List<Color>? gradientColors;
  final double? width;
  final double? height;

  const CostumButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.backgroundColor = Colors.blue,
    this.fontSize = 18,
    this.fontWeight = FontWeight.bold,
    this.textColor = Colors.white,
    this.gradientColors,
    this.width,
    this.height = 56,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonWidth = width ?? screenWidth * 0.8;

    // Create gradient colors based on backgroundColor if gradientColors not provided
    final List<Color> finalGradientColors = gradientColors ??
        [
          backgroundColor!,
          backgroundColor!.withValues(alpha: 0.8),
        ];

    return Container(
      width: buttonWidth,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: finalGradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: backgroundColor!.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(28),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: fontWeight,
                color: textColor,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
