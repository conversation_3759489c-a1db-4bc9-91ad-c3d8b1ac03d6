import 'package:flutter/material.dart';
import 'package:movie_map/feature/battle/arena_view.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/feature/profile/profile_view.dart';

mixin HomeBottomMixin on State<BottomNavBarPages> {
  int indexProvider = 0;

  final List<Widget> screens = [
    const HomeView(),
    const ArenaView(),
    const ProfileView(),
  ];

  void onTabTapped(int index) {
    setState(
      () {
        indexProvider = index;
      },
    );
  }
}
