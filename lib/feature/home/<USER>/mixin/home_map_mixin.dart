import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:movie_map/product/utility/constants/asset_path.dart';
import 'package:provider/provider.dart';
import 'package:movie_map/feature/home/<USER>/widget/home_map.dart';
import 'package:movie_map/product/state/provider/navigation_provider.dart';

mixin HomeMapMixin on State<MyMapScreen> {
  late String mapStyle;

  LatLng? _currentP;
  get currentP => _currentP;

  @override
  void dispose() {
    context.read<NavigationHelper>().mapController.future.then((value) => value.dispose());
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    context.read<NavigationHelper>().determinePosition(context).then((position) => setState(() {
          _currentP = LatLng(position.latitude, position.longitude);
        }));
    debugPrint('_currentP: $_currentP');

    rootBundle.loadString(AssetPath.mapStylePath).then((string) {
      mapStyle = string;
    });
  }
}
