import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:movie_map/product/state/provider/marker_manager.dart';
import 'package:provider/provider.dart';
import 'package:movie_map/feature/home/<USER>/mixin/home_map_mixin.dart';
import 'package:movie_map/product/state/provider/navigation_provider.dart';

class MyMapScreen extends StatefulWidget {
  const MyMapScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _MyMapScreenState createState() => _MyMapScreenState();
}

class _MyMapScreenState extends State<MyMapScreen> with HomeMapMixin {
  @override
  void initState() {
    super.initState();
    _loadCustomMarkerIcon();
  }

  LatLng? userLocation;

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;
    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: SizedBox(
        height: screenHeight,
        child: currentP == null
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    Gap(10),
                    Text('Loading map...'),
                  ],
                ),
              )
            : GoogleMap(
                compassEnabled: false,
                buildingsEnabled: false,
                fortyFiveDegreeImageryEnabled: false,
                myLocationEnabled: true,
                myLocationButtonEnabled: false,
                mapToolbarEnabled: true,
                onMapCreated: (GoogleMapController controller) async {
                  final mapController = context.read<NavigationHelper>().mapController;
                  if (!mapController.isCompleted) {
                    debugPrint('Map controller will be completed');
                    await mapController.complete(controller);
                  } else {
                    debugPrint('Map controller is already completed');
                  }
                  // ignore: deprecated_member_use
                  controller.setMapStyle(mapStyle);

                  await _loadMarkersFromFirebase();
                },
                initialCameraPosition: CameraPosition(
                  target: currentP!,
                  zoom: 15,
                ),
                markers: context.watch<MarkerManager>().markers.isNotEmpty ? Set<Marker>.of(context.watch<MarkerManager>().markers.values) : {},
                circles: context.watch<MarkerManager>().circles,
              ),
      ),
    );
  }

  // Load custom marker icon from assets
  Future<void> _loadCustomMarkerIcon() async {
    final BitmapDescriptor customIcon = await BitmapDescriptor.asset(
      const ImageConfiguration(size: Size(36, 36)),
      'assets/movie_icon.png',
    );

    setState(() {
      context.read<MarkerManager>().markerIcon = customIcon;
    });
  }

  Future<void> _loadMarkersFromFirebase() async {
    try {
      final markerManager = context.read<MarkerManager>();
      Position currentLocation = await context.read<NavigationHelper>().determinePosition(context);

      userLocation = LatLng(currentLocation.latitude, currentLocation.longitude);

      // Create circles first
      final Set<Circle> newCircles = {
        Circle(
          circleId: const CircleId('premium-area'),
          center: userLocation!,
          radius: 600,
          fillColor: Colors.white.withOpacity(0.3),
          strokeColor: Colors.grey,
          strokeWidth: 2,
        ),
        // TODO: Add non-premium circle for non premium users
        // Circle(
        //   circleId: const CircleId('non-premium-area'),
        //   center: userLocation!,
        //   radius: 200,
        //   fillColor: Colors.white.withOpacity(0.3),
        //   strokeColor: Colors.grey,
        //   strokeWidth: 1,
        // ),
      };

      markerManager.updateCircles(newCircles);

      // Get the current user's ID from FirebaseAuth
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception("User not logged in");
      }

      // Fetch the user's marker locations from Firestore
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(currentUser.uid).get();

      if (!userDoc.exists) {
        throw Exception("User document not found in Firestore");
      }

      final data = userDoc.data() as Map<String, dynamic>;
      final List<LatLng> allLocations = [];

      if (data['markerLocations'] != null) {
        final List<dynamic> markerLocations = data['markerLocations'];
        allLocations.addAll(
          markerLocations.map((location) {
            final GeoPoint geoPoint = location as GeoPoint;
            return LatLng(geoPoint.latitude, geoPoint.longitude);
          }),
        );
      }

      // Add all markers in one batch
      if (allLocations.isNotEmpty) {
        markerManager.addMarker(allLocations, context);
      }
    } catch (e) {
      debugPrint('Error loading markers: $e');
      // Handle error appropriately
    }
  }
}
