part of '../home_view.dart';

class BottomNavBarPages extends StatefulWidget {
  const BottomNavBarPages({
    super.key,
  });

  @override
  State<BottomNavBarPages> createState() => _BottomNavBarPagesState();
}

class _BottomNavBarPagesState extends State<BottomNavBarPages> with HomeBottomMixin {
  final String _moviesLabel = 'Map';
  final String _profileLabel = 'Profile';
  final String _battleLabel = 'Battle';
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        extendBody: false,
        body: IndexedStack(
          index: indexProvider,
          children: screens,
        ),
        bottomNavigationBar: BottomNavigationBar(
          elevation: 24,
          currentIndex: indexProvider,
          items: <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: const Icon(
                Icons.map_outlined,
              ),
              label: _moviesLabel,
            ),
            BottomNavigationBarItem(
              icon: Icon(indexProvider == 1 ? Icons.sports_esports : Icons.sports_esports_outlined),
              label: _battleLabel,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.star_rounded),
              label: _profileLabel,
            ),
          ],
          onTap: onTabTapped,
        ),
      ),
    );
  }
}
