import 'package:flutter/material.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/service/movie%20service/movie_repository.dart';

class MovieCollectionViewModel extends ChangeNotifier {
  bool isLoading = true;
  List<MovieModel> movies = [];
  final MovieRepository movieRepository;
  // TODO: Cancel the movie repository method. Use methods from user service. This process is clear.

  MovieCollectionViewModel(this.movieRepository);

  Future<void> fetchUserDataAndMovies(String userId) async {
    try {
      isLoading = true;
      notifyListeners();
      // Fetch data
      final user = await movieRepository.getUserData(userId);
      if (user != null) {
        movies = await movieRepository.getUserMovieCollection(user);
      }
    } catch (e) {
      debugPrint('Error fetching data: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
}
