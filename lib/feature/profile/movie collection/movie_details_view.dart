import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/feature/profile/components/movie_tile.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:provider/provider.dart';

class MovieDetailsView extends StatelessWidget {
  final MovieModel movie;
  const MovieDetailsView({
    super.key,
    required this.movie,
  });

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 40,
          actions: [
            WatchLaterController(movie: movie),
          ],
        ),
        body: Stack(
          children: [
            SizedBox(
              width: screenWidth,
              child: ShaderMask(
                shaderCallback: (rect) {
                  return LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      AppTheme.background,
                    ],
                  ).createShader(rect);
                },
                blendMode: BlendMode.darken,
                child: Image.network(
                  'https://image.tmdb.org/t/p/w500${movie.backdropPath}',
                  fit: BoxFit.fitHeight,
                ),
              ),
            ),
            SingleChildScrollView(
              child: Center(
                child: Padding(
                  padding: EdgeInsets.only(top: screenHeight * 0.2),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blueGrey.shade900,
                      borderRadius: AppTheme.borderRadiusAll,
                    ),
                    width: screenWidth * 0.8,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          movie.title,
                          style: const TextStyle(
                            fontSize: 30.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Gap(4),
                        Text(
                          movie.releaseDate,
                          style: const TextStyle(
                            fontSize: 18.0,
                            color: Colors.grey,
                          ),
                          textAlign: TextAlign.left,
                        ),
                        const Gap(4),
                        Row(
                          children: [
                            ...List.generate(
                              movie.genres.length * 2 - 1,
                              (index) {
                                if (index.isEven) {
                                  return Text(
                                    movie.genres[index ~/ 2],
                                    style: const TextStyle(
                                      fontSize: 18.0,
                                      color: Colors.grey,
                                    ),
                                    textAlign: TextAlign.left,
                                  );
                                } else {
                                  return const Text(
                                    ' | ',
                                    style: TextStyle(
                                      fontSize: 18.0,
                                      color: Colors.grey,
                                    ),
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 18.0),
                          child: StarRating(
                            voteAverage: movie.voteAverage,
                            voteCount: movie.voteCount,
                            iconSize: 24,
                          ),
                        ),
                        Text(
                          movie.overview,
                          style: const TextStyle(
                            fontSize: 18.0,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class WatchLaterController extends StatefulWidget {
  const WatchLaterController({
    super.key,
    required this.movie,
  });

  final MovieModel movie;

  @override
  State<WatchLaterController> createState() => _WatchLaterControllerState();
}

class _WatchLaterControllerState extends State<WatchLaterController> {
  bool isInWatchLater = false;

  @override
  void initState() {
    super.initState();

    isInWatchLater = context.read<UserManagerProvider>().userModel?.watchLater?.contains(widget.movie.id.toString()) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final UserManagerProvider userManagerProvider = context.read<UserManagerProvider>();

    return IconButton(
      icon: Icon(
        isInWatchLater ? Icons.check : Icons.add,
      ),
      onPressed: () {
        final User? user = FirebaseAuth.instance.currentUser;
        if (isInWatchLater) {
          userManagerProvider.removeMovieFromWatchLater(user!.uid, widget.movie.id.toString());
          setState(() {
            isInWatchLater = false;
          });
        } else {
          userManagerProvider.addMovieToWatchLater(user!.uid, widget.movie.id.toString());
          setState(() {
            isInWatchLater = true;
          });
        }
      },
    );
  }
}
