import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/product/theme/theme.dart';

class AchievementsComponent extends StatelessWidget {
  const AchievementsComponent({super.key});

  final String _achievementsHeader = '💪 Your Achievements';

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 96,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: AppTheme.surface,
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _achievementsHeader,
                style: const TextStyle(
                  fontSize: 20,
                ),
              ),
              const Spacer(),
              const Icon(
                Icons.arrow_forward_ios,
                size: 18,
              ),
            ],
          ),
          const Gap(4),
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                valueComponent(iconText: '📢', label: '3'),
                valueComponent(iconText: '🎤', label: '5'),
                valueComponent(iconText: '🍿', label: '17'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Row valueComponent({required String iconText, required String label}) {
    const double fontSize = 24;
    return Row(
      children: [
        Text(
          iconText,
          style: const TextStyle(fontSize: fontSize),
        ),
        const Gap(12),
        Text(
          label,
          style: const TextStyle(fontSize: fontSize),
        ),
      ],
    );
  }
}
