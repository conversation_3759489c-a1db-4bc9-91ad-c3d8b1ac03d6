import 'package:flutter/material.dart';
import 'package:movie_map/product/theme/theme.dart';

class CostumListTile extends StatelessWidget {
  final String label;
  final void Function()? onTap;

  const CostumListTile({
    super.key,
    required this.label,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: AppTheme.surface,
        ),
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
            const Spacer(),
            const Icon(
              Icons.arrow_forward_ios,
              size: 18,
            ),
          ],
        ),
      ),
    );
  }
}
