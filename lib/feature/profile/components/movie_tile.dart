import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/theme/theme.dart';

class MovieTile extends StatelessWidget {
  final MovieModel movie;
  final double? width;

  const MovieTile({
    super.key,
    required this.movie,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(12),
          ClipRRect(
            borderRadius: AppTheme.borderRadiusAll,
            child: Image.network(
              'https://image.tmdb.org/t/p/w500${movie.posterPath}',
              fit: BoxFit.cover,
              width: width,
            ),
          ),
          const Gap(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  movie.title,
                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Text(movie.releaseDate),
                const SizedBox(height: 8),
                StarRating(
                  voteAverage: movie.voteAverage,
                  voteCount: movie.voteCount,
                ),
                // Text(
                //   movie.overview,
                //   maxLines: 3,
                //   overflow: TextOverflow.ellipsis,
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class StarRating extends StatelessWidget {
  final double voteAverage;
  final int voteCount;
  final double iconSize;

  const StarRating({
    super.key,
    required this.voteAverage,
    required this.voteCount,
    this.iconSize = 18,
  });

  @override
  Widget build(BuildContext context) {
    // The maximum rating is 5, so we scale the voteAverage (which is out of 10)
    double starRating = (voteAverage / 2).clamp(0.0, 5.0);
    int fullStars = starRating.floor();
    bool hasHalfStar = (starRating - fullStars) >= 0.5;

    return Row(
      children: [
        // Display full stars
        for (int i = 0; i < fullStars; i++) Icon(Icons.star, color: Colors.yellow, size: iconSize),
        // Display a half star if needed
        if (hasHalfStar) Icon(Icons.star_half, color: Colors.yellow, size: iconSize),
        // Display empty stars for the remainder
        for (int i = 0; i < (5 - fullStars - (hasHalfStar ? 1 : 0)); i++) Icon(Icons.star_border, color: Colors.yellow, size: iconSize),
        // Display the voteAverage and voteCount next to the stars
        Text(
          ' ${voteAverage.toString().substring(0, 3)} ($voteCount)',
          style: TextStyle(fontSize: iconSize * 0.75, color: Colors.white),
        ),
      ],
    );
  }
}
