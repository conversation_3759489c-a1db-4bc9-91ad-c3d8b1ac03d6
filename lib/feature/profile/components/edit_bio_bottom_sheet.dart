import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:provider/provider.dart';

class EditBioBottomSheet extends StatefulWidget {
  const EditBioBottomSheet({super.key});

  @override
  State<EditBioBottomSheet> createState() => _EditBioBottomSheetState();
}

class _EditBioBottomSheetState extends State<EditBioBottomSheet> {
  TextEditingController descriptionController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: MediaQuery.of(context).viewInsets,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                hintText: 'Write something about you!',
              ),
            ),
            const Si<PERSON><PERSON><PERSON>(height: 10),
            Row(
              children: [
                const Spacer(),
                ElevatedButton(
                  onPressed: () {
                    final User? user = FirebaseAuth.instance.currentUser;
                    context.read<UserManagerProvider>().updateUserDescription(user!.uid, descriptionController.text);

                    Navigator.pop(context);
                  },
                  child: const Text('Approve'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
