import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/core/components/circle_profile_component.dart';
import 'package:movie_map/feature/profile/components/edit_bio_bottom_sheet.dart';
import 'package:movie_map/product/model/user_model.dart';

class ProfileDetails extends StatefulWidget {
  final UserModel user;
  const ProfileDetails({
    super.key,
    required this.user,
  });

  @override
  State<ProfileDetails> createState() => _ProfileDetailsState();
}

class _ProfileDetailsState extends State<ProfileDetails> {
  final String _addBio = '➕ Add Bio';

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          CircleProfileComponent(
            letter: widget.user.username.substring(0, 1).toUpperCase(),
            isEditingAvailable: true,
          ),
          const Gap(16),
          SizedBox(
            width: MediaQuery.of(context).size.width - 190,
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      widget.user.username,
                      style: const TextStyle(fontSize: 21, fontWeight: FontWeight.bold),
                    ),
                    const Spacer(),
                  ],
                ),
                Row(
                  children: [
                    profileValue(iconText: '💰', value: widget.user.money, context: context),
                    const Gap(16),
                    profileValue(iconText: '👑', value: widget.user.level, context: context),
                    const Gap(16),
                    profileValue(iconText: '🍿', value: widget.user.popcornSend, context: context),
                    const Spacer(),
                  ],
                ),
                widget.user.profileDescription == null
                    ? Row(
                        children: [
                          GestureDetector(
                            onTap: () async {
                              await _showBottomSheet(context);
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              child: Text(
                                _addBio,
                                style: const TextStyle(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                          const Spacer(),
                        ],
                      )
                    : GestureDetector(
                        onDoubleTap: () async {
                          await _showBottomSheet(context);
                        },
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width - 160,
                          child: Text(widget.user.profileDescription!),
                        ),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showBottomSheet(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return const EditBioBottomSheet();
      },
    );
  }

  Row profileValue({
    required BuildContext context,
    required String iconText,
    required int value,
  }) {
    const double fontSize = 16;
    return Row(
      children: [
        Text(
          iconText,
          style: const TextStyle(
            fontSize: fontSize,
          ),
        ),
        const Gap(4),
        Text(
          value.toString(),
          style: const TextStyle(
            fontSize: fontSize,
          ),
        ),
      ],
    );
  }
}
