import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/feature/battle-history/battle_history_view.dart';
import 'package:movie_map/feature/invitation-view/invitation_code_view.dart';
import 'package:movie_map/feature/profile/components/costum_tile.dart';
import 'package:movie_map/feature/profile/components/movie_tile.dart';
import 'package:movie_map/feature/profile/components/profile_details.dart';
import 'package:movie_map/feature/profile/movie%20collection/movie_collection_view.dart';
import 'package:movie_map/feature/profile/viewmodel/profile_view_model.dart';
import 'package:movie_map/feature/settings/view/settings_view.dart';
import 'package:movie_map/product/model/user_model.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:provider/provider.dart';

class ProfileView extends StatefulWidget {
  const ProfileView({super.key});

  @override
  State<ProfileView> createState() => _ProfileViewState();
}

class _ProfileViewState extends State<ProfileView> {
  final String _battles = '⚔️ Your battles';

  late final profileProvider = context.read<MovieCollectionViewModel>();
  late final userManagerProvider = context.read<UserManagerProvider>();

  @override
  void initState() {
    super.initState();
    //? Is this method correct for "setState() or markNeedsBuild() called during build." problem?
    final User? user = FirebaseAuth.instance.currentUser;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      profileProvider.fetchUserDataAndMovies(user!.uid);
    });
    userManagerProvider.fetchUserData(user!.uid);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 32,
        backgroundColor: AppTheme.transparent,
        leading: GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SettingsView(),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            color: Colors.transparent,
            child: const Icon(Icons.more_horiz),
          ),
        ),
      ),
      body: Consumer<UserManagerProvider>(
        builder: (context, provider, child) {
          final user = provider.userModel;

          if (user == null) {
            return const Center(child: CircularProgressIndicator());
          }

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: ListView(
              children: [
                ProfileDetails(user: user),
                const Gap(16),
                CostumListTile(
                  label: _battles,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const BattleHistoryView(),
                      ),
                    );
                  },
                ),
                const Gap(16),
                GestureDetector(
                  onTap: () async {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const InvitationCodeView(),
                      ),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: AppTheme.main,
                    ),
                    padding: const EdgeInsets.all(12),
                    child: const Center(
                      child: Text(
                        'Invite Friends & Get Premium',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.greenAccent,
                        ),
                      ),
                    ),
                  ),
                ),
                const Gap(16),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: AppTheme.borderRadiusAll,
                    color: AppTheme.surface,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Consumer<MovieCollectionViewModel>(
                      builder: (context, viewModel, child) {
                        if (viewModel.isLoading) {
                          return const Center(child: CircularProgressIndicator());
                        }
                        if (viewModel.movies.isEmpty) {
                          return const Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 4.0),
                              child: Text(
                                'There is no movie. 📦',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        }

                        final moviesToShow = viewModel.movies.take(8).toList();
                        return Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Collection 🎧',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => MovieCollectionView(
                                          movies: viewModel.movies,
                                        ),
                                      ),
                                    );
                                  },
                                  child: const Text(
                                    'See All',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            ListView.builder(
                              itemCount: moviesToShow.length,
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                final movie = moviesToShow[index];
                                return MovieTile(
                                  movie: movie,
                                  width: 80,
                                );
                              },
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
                const Gap(18),
              ],
            ),
          );
        },
      ),
    );
  }

  // Future<Uri> createDynamicLink(String username) async {
  //   final DynamicLinkParameters parameters = DynamicLinkParameters(
  //     uriPrefix: 'https://moviemap.page.link',
  //     link: Uri.parse('https://yourapp.com/invite?inviter=$username'),
  //     androidParameters: const AndroidParameters(
  //       packageName: 'com.example.yourapp',
  //       minimumVersion: 1,
  //     ),
  //     iosParameters: const IOSParameters(
  //       bundleId: 'com.example.yourapp',
  //       appStoreId: '123456789',
  //       minimumVersion: '1.0.0',
  //     ),
  //   );

  //   final ShortDynamicLink shortDynamicLink = await FirebaseDynamicLinks.instance.buildShortLink(parameters);
  //   final Uri dynamicUrl = shortDynamicLink.shortUrl;

  //   debugPrint("Generated Dynamic Link: $dynamicUrl");
  //   return dynamicUrl;
  // }

  Future<UserModel?> getUserData(String userId) async {
    DocumentSnapshot doc = await FirebaseFirestore.instance.collection('users').doc(userId).get();
    if (doc.exists) {
      return UserModel.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }
}
