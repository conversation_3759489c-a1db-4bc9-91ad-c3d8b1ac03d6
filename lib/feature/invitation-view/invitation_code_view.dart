import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InvitationCodeView extends StatelessWidget {
  const InvitationCodeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          // Info Card
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'Share your invitation codes with friends!',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'When someone uses your code, you\'ll both receive rewards.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () => generateInviteCode(context),
                      icon: const Icon(Icons.add),
                      label: const Text('Generate New Code'),
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size(double.infinity, 45),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // List of Codes
          Expanded(
            child: StreamBuilder<DocumentSnapshot>(
              stream: FirebaseFirestore.instance.collection('users').doc(FirebaseAuth.instance.currentUser?.uid).snapshots(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return const Center(child: Text('Something went wrong'));
                }

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final userData = snapshot.data?.data() as Map<String, dynamic>?;
                final codes = userData?['invitationCodes'] as List<dynamic>? ?? [];

                if (codes.isEmpty) {
                  return const Center(
                    child: Text('No invitation codes yet'),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: codes.length,
                  itemBuilder: (context, index) {
                    return StreamBuilder<QuerySnapshot>(
                      stream: FirebaseFirestore.instance.collection('invitationCodes').where('code', isEqualTo: codes[index]).snapshots(),
                      builder: (context, codeSnapshot) {
                        if (!codeSnapshot.hasData) {
                          return const SizedBox.shrink();
                        }

                        final codeData = codeSnapshot.data?.docs.first.data() as Map<String, dynamic>?;
                        final isUsed = codeData?['usedBy'] != null;

                        return Card(
                          child: ListTile(
                            title: Text(
                              codes[index],
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1.5,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isUsed ? 'Successfully Used! 🎉' : 'Available',
                                  style: TextStyle(
                                    color: isUsed ? Colors.blue : Colors.orange,
                                    fontWeight: isUsed ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                                if (isUsed && codeData?['usedBy'] != null)
                                  FutureBuilder<DocumentSnapshot>(
                                    future: FirebaseFirestore.instance.collection('users').doc(codeData?['usedBy']).get(),
                                    builder: (context, userSnapshot) {
                                      if (!userSnapshot.hasData) return const SizedBox.shrink();

                                      final userData = userSnapshot.data?.data() as Map<String, dynamic>?;
                                      return Text(
                                        'Used by: ${userData?['username'] ?? 'Unknown'}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      );
                                    },
                                  ),
                              ],
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.copy),
                              onPressed: () {
                                Clipboard.setData(ClipboardData(text: codes[index]));
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Code copied to clipboard'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              },
                              tooltip: 'Copy code',
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> generateInviteCode(BuildContext context) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    String code = generateRandomCode();

    try {
      await FirebaseFirestore.instance.collection('invitationCodes').add({
        'code': code,
        'usedBy': null,
        'createdAt': FieldValue.serverTimestamp(),
        'createdBy': currentUser!.uid,
      });

      await FirebaseFirestore.instance.collection('users').doc(currentUser.uid).update({
        'invitationCodes': FieldValue.arrayUnion([code]),
      });

      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '🎉 New invitation code generated!',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text('Code: $code'),
              const Text('Tap to copy', style: TextStyle(fontSize: 12)),
            ],
          ),
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          action: SnackBarAction(
            label: 'Copy',
            onPressed: () {
              Clipboard.setData(ClipboardData(text: code));
              if (!context.mounted) return;
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Code copied to clipboard'),
                  duration: Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        ),
      );
    } catch (e) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Error generating invitation code. Please try again.'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

// Rastgele alfanümerik davet kodu oluşturma
  String generateRandomCode() {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    Random random = Random();
    return List.generate(8, (index) => characters[random.nextInt(characters.length)]).join();
  }
}
