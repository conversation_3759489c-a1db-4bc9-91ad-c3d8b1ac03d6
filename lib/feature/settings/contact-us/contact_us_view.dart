import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/core/helper/costum_button.dart';
import 'package:movie_map/product/service/form-service/form_service.dart';

class ContactUsView extends StatefulWidget {
  const ContactUsView({super.key});

  @override
  State<ContactUsView> createState() => _ContactUsViewState();
}

class _ContactUsViewState extends State<ContactUsView> {
  bool isFormAvailable = false;

  final TextEditingController topicController = TextEditingController();

  final TextEditingController messageController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            children: [
              const Gap(36),
              const Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    'How can we help you?',
                    style: TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const Gap(24),
              const Text(
                'Please fill out the form below and we will get back to you as soon as possible.',
                style: TextStyle(
                  fontSize: 16,
                ),
              ),
              const Gap(12),
              TextField(
                controller: topicController,
                onChanged: (value) {
                  _checkFormAvailability();
                },
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
                decoration: InputDecoration(
                  hintText: 'Topic',
                  hintStyle: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white.withOpacity(0.5),
                  ),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  filled: true,
                  fillColor: Colors.grey.shade900,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
              const Gap(36),
              const Text(
                'Explain your problem to us as clearly as possible and we will help you.',
                style: TextStyle(
                  fontSize: 16,
                ),
              ),
              const Gap(12),
              TextField(
                controller: messageController,
                onChanged: (value) {
                  _checkFormAvailability();
                },
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: 'Your message',
                  hintStyle: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.5),
                  ),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  filled: true,
                  fillColor: Colors.grey.shade900,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
              Gap(MediaQuery.of(context).size.height * 0.25),
              CostumButton(
                onPressed: () {
                  FormService.saveFormData(context, topicController.text, messageController.text);
                },
                text: 'Send',
                backgroundColor: isFormAvailable ? Colors.blue.shade900 : Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // control the form availability
  void _checkFormAvailability() {
    if (topicController.text.isNotEmpty && messageController.text.isNotEmpty) {
      setState(() {
        isFormAvailable = true;
      });
    } else {
      setState(() {
        isFormAvailable = false;
      });
    }
  }
}
