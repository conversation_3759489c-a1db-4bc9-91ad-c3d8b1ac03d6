import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/core/helper/authentication_helpers.dart';
import 'package:movie_map/feature/onboard/sign_up.dart';
import 'package:movie_map/feature/settings/contact-us/contact_us_view.dart';
import 'package:movie_map/feature/settings/view/account_deletion_webview.dart';
import 'package:movie_map/product/theme/theme.dart';

class SettingsView extends StatefulWidget {
  const SettingsView({super.key});

  @override
  State<SettingsView> createState() => _SettingsViewState();
}

class _SettingsViewState extends State<SettingsView> {
  final String _settingsHeader = 'Settings';
  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _settingsHeader,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Center(
        child: Column(
          children: [
            const Gap(36),
            // const CostumSettingTile(
            //   label: 'Notifications',
            //   iconData: '🔔',
            //   hasSwitch: true,
            // ),
            // _divider(),
            // const CostumSettingTile(
            //   label: 'Haptics',
            //   iconData: '📱',
            //   hasSwitch: true,
            // ),
            // _divider(),
            //TODO: subscription is not working yet.
            // CostumSettingTile(
            //   label: 'Moviemap Pro',
            //   iconData: '⭐️',
            //   onTap: () {
            //     Navigator.push(
            //       context,
            //       MaterialPageRoute(
            //         builder: (context) {
            //           return const SubscriptionView();
            //         },
            //       ),
            //     );
            //   },
            // ),
            // _divider(),
            CostumSettingTile(
              label: 'Help Center',
              iconData: '⛑️',
              onTap: () {
                debugPrint('Contact us düğmesine basıldı');
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ContactUsView(),
                  ),
                );
              },
            ),
            _divider(),
            const CostumSettingTile(
              label: 'Rate Us',
              iconData: '💙',
            ),
            _divider(),
            CostumSettingTile(
              label: 'Delete Account',
              iconData: '🗑',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AccountDeletionWebView(),
                  ),
                );
              },
            ),
            _divider(),
            CostumSettingTile(
              label: 'Log Out',
              iconData: '🚪',
              onTap: () async {
                await AuthenticationHelpers.signOut();

                // ScaffoldMessenger.of(context).showSnackBar(
                //   const SnackBar(
                //     content: Text('You have been logged out.'),
                //   ),
                // );

                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (_) => const SignUpView(),
                  ),
                );
              },
            ),
            Gap(screenHeight * 0.08),
            const CostumSettingTile(
              label: 'Terms of Service',
              iconData: 'ℹ',
            ),
            _divider(),
            const CostumSettingTile(
              label: 'Privacy Policy',
              iconData: '🔒',
            ),
          ],
        ),
      ),
    );
  }

  // divider function
  Divider _divider() {
    return Divider(
      color: Colors.grey.shade900,
      thickness: 1,
      indent: 20,
      endIndent: 20,
    );
  }
}

class CostumSettingTile extends StatelessWidget {
  final String label;
  final String iconData;
  final bool hasSwitch;
  final VoidCallback? onTap;
  final IconData? navigatorIcon;

  const CostumSettingTile({
    super.key,
    required this.label,
    required this.iconData,
    this.hasSwitch = false,
    this.onTap,
    this.navigatorIcon = Icons.arrow_forward_ios,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 6,
        ),
        color: Colors.transparent,
        child: Row(
          children: [
            Text(
              iconData,
              style: const TextStyle(
                fontSize: 22,
              ),
            ),
            const Gap(16),
            Text(
              label,
              style: TextStyle(
                fontSize: 22,
                color: Colors.grey[300],
              ),
            ),
            if (hasSwitch) ...[
              const Spacer(),
              SizedBox(
                height: 24,
                child: Switch(
                  value: false,
                  onChanged: (value) {},
                  activeColor: AppTheme.primary,
                ),
              ),
            ] else ...[
              const Spacer(),
              Icon(
                navigatorIcon,
                color: Colors.grey.shade600,
              ),
              const Gap(16),
            ],
          ],
        ),
      ),
    );
  }
}
