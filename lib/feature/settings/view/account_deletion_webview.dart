import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AccountDeletionWebView extends StatefulWidget {
  const AccountDeletionWebView({super.key});

  @override
  State<AccountDeletionWebView> createState() => _AccountDeletionWebViewState();
}

class _AccountDeletionWebViewState extends State<AccountDeletionWebView> {
  late final WebViewController controller;

  @override
  void initState() {
    super.initState();
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(
        Uri.parse('https://movie-map-cb98d.web.app'),
      );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delete Account'),
      ),
      body: WebViewWidget(controller: controller),
    );
  }
}
