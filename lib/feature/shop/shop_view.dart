import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:movie_map/core/helper/costum_button.dart';
import 'package:movie_map/feature/invitation-view/invitation_code_view.dart';
import 'package:movie_map/feature/legal/legal_web_view.dart';

class ShopView extends StatefulWidget {
  const ShopView({super.key});

  @override
  State<ShopView> createState() => _ShopViewState();
}

class _ShopViewState extends State<ShopView> {
  final List<String> _productIds = [
    'coin_package_1000',
    'coin_package_3k',
    'coin_package_7k',
    'coin_package_15k',
    'coin_package_40k',
    'coin_package_100k',
  ];

  List<ProductDetails> _products = [];
  late Stream<List<PurchaseDetails>> _purchaseStream;
  late InAppPurchase _iap;

  @override
  void initState() {
    super.initState();
    _iap = InAppPurchase.instance;
    _purchaseStream = _iap.purchaseStream;
    _loadProducts();
    _listenToPurchaseUpdates();
  }

  Future<void> _loadProducts() async {
    final bool available = await _iap.isAvailable();
    if (!available) {
      print('In-app purchases not available.');
      return;
    }

    final ProductDetailsResponse response = await _iap.queryProductDetails(_productIds.toSet());
    if (response.error != null) {
      print('Error fetching products: ${response.error}');
      return;
    }

    if (response.productDetails.isEmpty) {
      print('No products found.');
      return;
    }

    setState(() {
      _products = response.productDetails;
    });
  }

  void _listenToPurchaseUpdates() {
    _purchaseStream.listen((purchases) {
      for (final purchase in purchases) {
        if (purchase.status == PurchaseStatus.purchased) {
          _deliverProduct(purchase);
        } else if (purchase.status == PurchaseStatus.error) {
          print('Purchase error: ${purchase.error}');
        }
      }
    });
  }

  void _deliverProduct(PurchaseDetails purchase) {
    final int coins = _getCoinsForProduct(purchase.productID);
    FirebaseFirestore.instance.collection('users').doc(FirebaseAuth.instance.currentUser?.uid).update({
      'money': FieldValue.increment(coins),
    });
  }

  String _getIconForProduct(String productId) {
    switch (productId) {
      case 'coin_package_1000':
        return '🍿';
      case 'coin_package_3k':
        return '🎫';
      case 'coin_package_7k':
        return '🎬';
      case 'coin_package_15k':
        return '💵';
      case 'coin_package_40k':
        return '💰';
      case 'coin_package_100k':
        return '📦';
      default:
        return '';
    }
  }

  int _getCoinsForProduct(String productId) {
    switch (productId) {
      case 'coin_package_1000':
        return 1000;
      case 'coin_package_3k':
        return 3000;
      case 'coin_package_7k':
        return 7000;
      case 'coin_package_15k':
        return 15000;
      case 'coin_package_40k':
        return 40000;
      case 'coin_package_100k':
        return 100000;
      default:
        return 0;
    }
  }

  void _buyProduct(ProductDetails product) {
    final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
    _iap.buyConsumable(
      purchaseParam: purchaseParam,
      autoConsume: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        children: [
          // Current Balance
          StreamBuilder<DocumentSnapshot>(
            stream: FirebaseFirestore.instance.collection('users').doc(FirebaseAuth.instance.currentUser?.uid).snapshots(),
            builder: (context, snapshot) {
              if (!snapshot.hasData) return const SizedBox.shrink();

              final userData = snapshot.data?.data() as Map<String, dynamic>?;
              final balance = userData?['money'] ?? 0;

              return Container(
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF1F1F1F), Color(0xFF2C2C2C)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.wallet,
                      size: 24,
                      color: Colors.amber,
                    ),
                    const Gap(8),
                    Text(
                      '$balance coins',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '🔥 Limited Time Offers!',
                  style: TextStyle(
                    color: Colors.redAccent,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          const Gap(16),
          Flexible(
            child: GridView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 0.67,
              ),
              itemCount: _products.length,
              itemBuilder: (context, index) {
                final product = _products[index];
                _products.sort((a, b) => _getSortOrder(a.id).compareTo(_getSortOrder(b.id)));
                return _CoinPackageCard(
                  package: CoinPackage(
                    coins: _getCoinsForProduct(product.id),
                    price: product.price,
                    icon: _getIconForProduct(product.id),
                  ),
                  onTap: () => _buyProduct(product),
                );
              },
            ),
          ),

          CostumButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const InvitationCodeView(),
                ),
              );
            },
            text: 'Or Invite Friends 👥',
            backgroundColor: Colors.amber,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            textColor: Colors.black,
          ),

          TextButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LegalWebView(),
                ),
              );
            },
            child: const Text(
              'Terms & Privacy',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ),
          const Gap(96),
        ],
      ),
    );
  }
}

class CoinPackage {
  final int coins;
  final String price;
  final String icon;

  CoinPackage({
    required this.coins,
    required this.price,
    this.icon = '📦',
  });

  String get formattedCoins {
    return coins > 1000 ? '${(coins / 1000).toStringAsFixed(0)}K' : coins.toString();
  }
}

class _CoinPackageCard extends StatelessWidget {
  final CoinPackage package;
  final VoidCallback onTap;

  const _CoinPackageCard({required this.package, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: const Color(0xFF1F1F1F),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade800),
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    package.formattedCoins,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Text(
                    'coins',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                  const Gap(4),
                  Text(
                    package.icon,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  const Gap(12),
                  ElevatedButton(
                    onPressed: onTap,
                    style: ElevatedButton.styleFrom(
                      // TODO: hangisi güzel olur bilemedim. birkaç kişiye sorulabilir.
                      // backgroundColor: Colors.grey.shade800,
                      backgroundColor: Colors.indigo.shade900,
                      minimumSize: const Size(double.infinity, 36),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: AutoSizeText(
                      package.price,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

int _getSortOrder(String productId) {
  switch (productId) {
    case 'coin_package_1000':
      return 1;
    case 'coin_package_3k':
      return 2;
    case 'coin_package_7k':
      return 3;
    case 'coin_package_15k':
      return 4;
    case 'coin_package_40k':
      return 5;
    case 'coin_package_100k':
      return 6;
    default:
      return 7;
  }
}
