import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:shimmer/shimmer.dart';

class SubscriptionView extends StatelessWidget {
  const SubscriptionView({super.key});

  final String _becomeAMember = 'Become VIP';
  final String _offerText = '₺169,99 after 7-day free trial';
  final String _continue = 'Continue ☑️';

  // TODO: Bring photo which user's interested
  final String imageUrl = 'https://image.tmdb.org/t/p/w500/8Y43POKjjKDGI9MH89NW0NAzzp8.jpg';

  @override
  Widget build(BuildContext context) {
    // final double screenWidth = MediaQuery.of(context).size.width;
    return SafeArea(
      bottom: false,
      child: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              ShaderMask(
                shaderCallback: (rect) {
                  return LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      AppTheme.background,
                    ],
                  ).createShader(rect);
                },
                blendMode: BlendMode.darken,
                child: SizedBox(
                  height: 0.22 * MediaQuery.of(context).size.height,
                  width: double.infinity,
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.fitWidth,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: AppTheme.surface,
                      highlightColor: Colors.blue.shade900,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: AspectRatio(
                          aspectRatio: 2.39,
                          child: Container(
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => const SizedBox.shrink(),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  children: [
                    const Gap(30),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 69, 36, 7).withOpacity(0.8),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _becomeAMember,
                        style: const TextStyle(
                          fontSize: 30,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Gap(50),
                    const TimelineStep(
                      isCompleted: true,
                      title: 'Install the app',
                      subtitle: 'You successfully started your Selfie-A-Day journey!',
                      icon: '📲',
                      iconColor: Colors.blue,
                    ),
                    const TimelineStep(
                      isCompleted: false,
                      title: 'Today: Get Full Access',
                      subtitle: 'Unlock unlimited private albums, photo import and more.',
                      icon: '🔓',
                      iconColor: Colors.blue,
                    ),
                    const TimelineStep(
                      isCompleted: false,
                      title: 'Day 5: Trial Reminder',
                      subtitle: 'We’ll remind you with a notification. Cancel anytime in just 15 seconds.',
                      icon: '⏰',
                      iconColor: Colors.grey,
                    ),
                    const TimelineStep(
                      isCompleted: false,
                      title: 'Day 7: Trial Ends',
                      subtitle: 'Your subscription will start on Oct 10.',
                      icon: '🎉',
                      iconColor: Colors.grey,
                    ),
                    const Gap(40),
                    Center(
                      child: Column(
                        children: [
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 40.0, vertical: 15.0),
                              textStyle: const TextStyle(
                                fontSize: 18,
                              ),
                            ),
                            onPressed: () {
                              // Continue action
                            },
                            child: Text(_continue),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            _offerText,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
        floatingActionButton: Padding(
          padding: const EdgeInsets.all(12.0),
          child: FloatingActionButton(
            backgroundColor: Colors.black.withOpacity(0.4),
            mini: true,
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Icon(
              Icons.close,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}

class TimelineStep extends StatelessWidget {
  final bool isCompleted;
  final String title;
  final String subtitle;
  final String icon;
  final Color iconColor;

  const TimelineStep({
    super.key,
    required this.isCompleted,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Text(
              icon,
              style: TextStyle(color: iconColor, fontSize: 24),
            ),
            if (!isCompleted) const VerticalDivider(color: Colors.grey, thickness: 2),
          ],
        ),
        const Gap(10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  // color: isCompleted ? Colors.grey : Colors.black,
                  decoration: isCompleted ? TextDecoration.lineThrough : TextDecoration.none,
                ),
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ],
    );
  }
}
