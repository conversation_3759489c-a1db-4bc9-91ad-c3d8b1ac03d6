import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/core/helper/authentication_helpers.dart';
import 'package:movie_map/feature/onboard/profile%20photo/profile_photo_page.dart';
import 'package:movie_map/product/state/provider/create_user_provider.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:movie_map/product/model/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class CreateUsernameView extends StatefulWidget {
  const CreateUsernameView({super.key});

  @override
  State<CreateUsernameView> createState() => _CreateUsernameViewState();
}

class _CreateUsernameViewState extends State<CreateUsernameView> {
  String username = '';
  final String _hintText = 'Cristopher';
  final String _pickHeader = 'Pick your\nusername!';
  // bool? isUsernameAvailable;
  String? errorDescription;

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      // TODO: is the center widget necessary?
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Gap(screenHeight * 0.2),
              Text(
                _pickHeader,
                style: const TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Gap(screenHeight * 0.2),
              SizedBox(
                width: screenWidth * 0.6,
                child: TextField(
                  onChanged: (text) async {
                    errorDescription = await AuthenticationHelpers().checkUsername(text);
                    setState(
                      () {
                        username = text;
                      },
                    );
                  },
                  maxLength: 16,
                  style: const TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  decoration: InputDecoration(
                    hintText: _hintText,
                    counterText: '',
                    hintStyle: const TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                    ),
                    border: InputBorder.none,
                  ),
                ),
              ),
              Gap(screenHeight * 0.2),
              if (errorDescription != null)
                Text(
                  errorDescription ?? 'Username is available',
                  style: TextStyle(
                    color: errorDescription != null ? Colors.green.shade800 : Colors.red,
                    fontSize: 14,
                  ),
                ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          if (username.isEmpty) {
            debugPrint('Content must be filled');
            return;
          } else {
            final user = FirebaseAuth.instance.currentUser;
            if (user != null) {
              // Get the pending invitation code
              final prefs = await SharedPreferences.getInstance();
              final pendingCode = prefs.getString('pendingInviteCode');

              // Initialize user with base money
              int initialMoney = 350;

              if (pendingCode != null) {
                try {
                  final batch = FirebaseFirestore.instance.batch();

                  // Check if code exists and is unused
                  final codeQuery = await FirebaseFirestore.instance.collection('invitationCodes').where('code', isEqualTo: pendingCode).get();

                  if (codeQuery.docs.isNotEmpty && codeQuery.docs.first['usedBy'] == null) {
                    final codeDoc = codeQuery.docs.first;

                    // Mark code as used
                    batch.update(codeDoc.reference, {
                      'usedBy': user.uid,
                      'usedAt': FieldValue.serverTimestamp(),
                    });

                    // Add invitation code bonus
                    initialMoney += 150;

                    // Add reward to code provider
                    final providerQuery = await FirebaseFirestore.instance.collection('users').where('invitationCodes', arrayContains: pendingCode).get();

                    if (providerQuery.docs.isNotEmpty) {
                      batch.update(providerQuery.docs.first.reference, {
                        'money': FieldValue.increment(150),
                      });
                    }

                    await batch.commit();

                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Invitation code bonus applied! +150 coins 🎉'),
                          duration: Duration(seconds: 3),
                        ),
                      );
                    }
                  }
                } catch (e) {
                  debugPrint('Error applying invitation code: $e');
                }
                await prefs.remove('pendingInviteCode');
              }

              // Create user with final money amount
              UserModel newUser = UserModel(
                username: username,
                money: initialMoney,
                level: 1,
                popcornSend: 0,
                hasReceivedWelcomeLootBox: false,
              );

              await AuthenticationHelpers().saveUserData(user.uid, newUser);
              context.read<CreateUserProvider>().user.username = username;

              if (!mounted) return;
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => const ProfilePhotoPage(
                    isOnboard: true,
                  ),
                ),
              );
            }
          }
        },
        child: const Icon(Icons.check),
      ),
    );
  }
}
