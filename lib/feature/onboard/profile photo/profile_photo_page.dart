import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:movie_map/core/components/circle_profile_component.dart';
import 'package:movie_map/core/helper/costum_button.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/feature/loot_box/loot_box_view.dart';
import 'package:permission_handler/permission_handler.dart';

class ProfilePhotoPage extends StatefulWidget {
  final bool isOnboard;

  const ProfilePhotoPage({
    super.key,
    this.isOnboard = false,
  });

  @override
  State<ProfilePhotoPage> createState() => _ProfilePhotoPageState();
}

class _ProfilePhotoPageState extends State<ProfilePhotoPage> {
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();
  // final FirebaseStorage _storage = FirebaseStorage.instance;

  final String _skip = 'Skip';
  final String _save = 'Save';
  final String _saveOnboard = 'Continue';
  final String _cancel = 'Cancel';
  final String _selectGallery = 'Select from Gallery';
  final String _takePhoto = 'Take Photo';
  final String _removePhoto = 'Remove Photo';

  bool _isAvailable = false;
  bool isUploading = false;

  final User? user = FirebaseAuth.instance.currentUser;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 60,
        actions: [
          isUploading
              ? const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(
                    color: Colors.blue,
                  ),
                )
              : GestureDetector(
                  onTap: () async {
                    if (widget.isOnboard) {
                      setState(() {
                        isUploading = true;
                      });
                      await _uploadProfilePhoto();

                      // Navigate to loot box for new users
                      Navigator.of(context).pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (context) => const LootBoxView(),
                        ),
                        (route) => false,
                      );

                      setState(() {
                        isUploading = false;
                      });
                    } else {
                      setState(() {
                        isUploading = true;
                      });

                      await _uploadProfilePhoto();
                      Navigator.of(context).pop();
                      isUploading = false;
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                    color: Colors.transparent,
                    child: Text(
                      widget.isOnboard && _isAvailable
                          ? _saveOnboard
                          : widget.isOnboard && !_isAvailable
                              ? _skip
                              : !widget.isOnboard && _isAvailable
                                  ? _save
                                  : _cancel,
                      style: const TextStyle(
                        color: Colors.blue,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // my firestore username is available.
            _selectedImage != null
                ? CircleProfileComponent(
                    file: _selectedImage,
                    isEditingAvailable: false,
                  )
                : const CircleProfileComponent(
                    letter: 'A', // TODO: Change this letter to the first letter of the username
                    isEditingAvailable: false,
                  ),
            const Gap(16),
            Text(
              'Add a profile photo',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade500,
              ),
            ),
            Gap(screenHeight * 0.32),
            CostumButton(
              onPressed: () => _pickImage(source: ImageSource.gallery),
              text: _selectGallery,
              fontSize: 18,
              backgroundColor: Colors.grey.shade900,
            ),
            const Gap(12),
            CostumButton(
              onPressed: () {
                if (_selectedImage == null) {
                  _pickImage(source: ImageSource.camera);
                }
              },
              text: _takePhoto,
              backgroundColor: Colors.grey.shade900,
              fontSize: 18,
            ),
            const Gap(12),
            CostumButton(
              onPressed: () {
                if (_selectedImage != null) {
                  setState(() {
                    _selectedImage = null;
                    _isAvailable = false;
                  });
                }
              },
              text: _removePhoto,
              backgroundColor: Colors.grey.shade900,
              fontSize: 18,
              textColor: Colors.red.shade700,
            ),
            Gap(screenHeight * 0.1),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage({required ImageSource source}) async {
    await _requestPermissions(source);
    final XFile? pickedFile = await _picker.pickImage(source: source);

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
      });
    }

    if (_selectedImage != null) {
      debugPrint('Image selected');
      setState(() {
        _isAvailable = true;
      });
    } else {
      debugPrint('No image selected');
    }
  }

  Future<void> _uploadProfilePhoto() async {
    try {
      final FirebaseFirestore firestore = FirebaseFirestore.instance;
      final userId = user!.uid;
      final storageRef = FirebaseStorage.instance.ref();

      final String storagePath = 'ProfilePhoto/pp$userId';
      final reference = storageRef.child(storagePath);

      if (_selectedImage != null) {
        debugPrint('Starting file upload...');

        // Metadata ekleyelim
        final metadata = SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'userId': userId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        );

        // Upload task'i metadata ile oluştur
        final UploadTask uploadTask = reference.putFile(_selectedImage!, metadata);

        // Progress izleme
        uploadTask.snapshotEvents.listen(
          (TaskSnapshot snapshot) {
            final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            debugPrint('Upload progress: $progress%');
          },
          onError: (error) {
            debugPrint('Upload error: $error');
          },
        );

        // Upload'ı bekle
        final snapshot = await uploadTask;
        debugPrint('File upload completed');

        // Download URL'i al
        final url = await snapshot.ref.getDownloadURL();
        debugPrint('Got download URL: $url');

        // Firestore'a kaydet
        await firestore.collection('users').doc(userId).update({'profilePhoto': url});
        debugPrint('Profile photo URL saved to Firestore');

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile photo uploaded successfully! ✅')),
        );
      }
    } catch (e) {
      debugPrint('Error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to upload photo: $e')),
      );
    }
  }

  Future<void> _requestPermissions(ImageSource source) async {
    if (source == ImageSource.camera && await Permission.camera.isGranted) {
      PermissionStatus cameraStatus = await Permission.camera.request();
      if (cameraStatus.isDenied || cameraStatus.isPermanentlyDenied) {
        openAppSettings();
      }
    } else if (source == ImageSource.gallery && await Permission.photos.isGranted) {
      //TODO: Check if android versions are different
      PermissionStatus photosStatus = await Permission.photos.request();
      if (photosStatus.isDenied || photosStatus.isPermanentlyDenied) {
        openAppSettings();
      }
    }
  }
}
