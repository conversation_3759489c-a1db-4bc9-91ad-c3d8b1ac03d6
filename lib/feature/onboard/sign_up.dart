import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/core/helper/authentication_helpers.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/feature/onboard/username/create_username_view.dart';
import 'package:movie_map/product/model/user_model.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:movie_map/product/utility/constants/asset_path.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SignUpView extends StatefulWidget {
  const SignUpView({super.key});

  @override
  State<SignUpView> createState() => _SignUpViewState();
}

class _SignUpViewState extends State<SignUpView> {
  final String _movieMap = 'Movie Map';
  final String _manifest = 'Find, Build, & Battle with\nyour Dream Collection';
  final String _legalText = 'By proceeding, you agree to our Terms of Service and Privacy Policy. We collect location data to enable gameplay even when the app is closed or not in use.';
  final String _signWithGoogleButtonText = 'Sign In With Google';
  final String _invitationCode = 'Enter Invitation Code';
  final String _submit = '✔️ Code Submitted';

  final bool _isValidCodeSubmitted = false;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Calculate responsive image height
    final double maxImageHeight = screenHeight * 0.4;
    const double minImageHeight = 200;
    final double imageHeight = maxImageHeight < minImageHeight ? minImageHeight : maxImageHeight;

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Column(
              children: [
                SizedBox(height: screenHeight * 0.04),
                Text(
                  _movieMap,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: screenHeight * 0.02),
                ClipRRect(
                  borderRadius: AppTheme.borderRadiusAll * 4,
                  child: SizedBox(
                    height: imageHeight,
                    width: screenWidth * 0.8,
                    child: Image.asset(
                      AssetPath.onboardImage,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(height: screenHeight * 0.02),
                Text(
                  _manifest,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: screenHeight * 0.02),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Text(
                    _legalText,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 10, color: Colors.grey),
                  ),
                ),
                SizedBox(height: screenHeight * 0.03),
                Container(
                  width: screenWidth * 0.8,
                  height: 56,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.main,
                        AppTheme.deepMain,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(28),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.main.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () async {
                        try {
                          final userCredential = await AuthenticationHelpers().signWithGoogle();
                          if (!context.mounted) return;

                          if (userCredential == null) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text("Sign in cancelled.")),
                            );

                            debugPrint('User credential is null.');
                            return;
                          }

                          final uid = userCredential.user?.uid;
                          debugPrint('User id check: $uid');
                          if (uid == null) return;

                          final doc = await FirebaseFirestore.instance.collection('users').doc(uid).get();

                          debugPrint("Is user exists: ${doc.exists}");

                          Navigator.of(context).pushAndRemoveUntil(
                            MaterialPageRoute(
                              builder: (_) => doc.exists ? const BottomNavBarPages() : const CreateUsernameView(),
                            ),
                            (route) => false,
                          );
                        } catch (e) {
                          if (!context.mounted) return;

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text("Bir hata oluştu: $e")),
                          );
                        }
                      },
                      borderRadius: BorderRadius.circular(28),
                      child: Center(
                        child: Text(
                          _signWithGoogleButtonText,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (_) => _InvitationCodeDialog(),
                    );
                  },
                  child: Text(
                    _isValidCodeSubmitted ? _submit : _invitationCode,
                    style: TextStyle(
                      fontSize: 12,
                      color: _isValidCodeSubmitted ? Colors.green : null,
                    ),
                  ),
                ),
                SizedBox(height: screenHeight * 0.02),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

final class _InvitationCodeDialog extends StatelessWidget {
  _InvitationCodeDialog();

  final TextEditingController invitationCodeController = TextEditingController();
  final String _callToAction = 'Enter Invitation Code';
  final String _hintCall = 'Enter';
  final String _submit = 'Submit';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_callToAction),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: invitationCodeController,
            decoration: InputDecoration(
              hintText: _hintCall,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.main,
                  AppTheme.deepMain,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.main.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () async {
                  final invitationCode = invitationCodeController.text.trim();
                  if (invitationCode.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Code cannot be empty')),
                    );
                    return;
                  }

                  if (invitationCode == 'TESTUSER') {
                    try {
                      // Sign in with test credentials
                      await FirebaseAuth.instance.signInWithEmailAndPassword(
                        email: '<EMAIL>',
                        password: 'testuser123.',
                      );

                      debugPrint('Signed in with test account.');

                      // Check if test user document exists
                      final userDoc = await FirebaseFirestore.instance.collection('users').doc('testuseridforaccess').get();

                      if (!context.mounted) return;

                      if (!userDoc.exists) {
                        UserModel newUser = UserModel(
                          username: 'testUser',
                          money: 500,
                          level: 1,
                          popcornSend: 0,
                        );
                        // create user document with test user id
                        await FirebaseFirestore.instance.collection('users').doc('rTrrLNPLIyPoOSiNE7JWJfFdEvE2').set(newUser.toMap());

                        Navigator.of(context).pushAndRemoveUntil(
                          MaterialPageRoute(
                            builder: (_) => const BottomNavBarPages(),
                          ),
                          (route) => false,
                        );
                      } else {
                        // If user document exists, go to main app
                        Navigator.of(context).pushAndRemoveUntil(
                          MaterialPageRoute(
                            builder: (_) => const BottomNavBarPages(),
                          ),
                          (route) => false,
                        );
                      }
                    } catch (e) {
                      if (!context.mounted) return;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Error signing in with test account'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                    return;
                  }

                  try {
                    // Only check if code exists and is unused
                    final QuerySnapshot snapshot = await FirebaseFirestore.instance.collection('invitationCodes').where('code', isEqualTo: invitationCode).get();

                    if (snapshot.docs.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Invalid invitation code')),
                      );
                      return;
                    }

                    final doc = snapshot.docs.first;
                    final usedBy = doc['usedBy'];

                    if (usedBy != null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('This code has already been used')),
                      );
                      return;
                    }

                    // Store the valid code temporarily
                    // We'll apply it after successful sign up
                    await SharedPreferences.getInstance().then((prefs) {
                      prefs.setString('pendingInviteCode', invitationCode);
                    });

                    Navigator.pop(context); // Close dialog
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Code verified! It will be applied after sign up.'),
                        duration: Duration(seconds: 3),
                      ),
                    );
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Error checking invitation code')),
                    );
                  }
                },
                borderRadius: BorderRadius.circular(24),
                child: Center(
                  child: Text(
                    _submit,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
