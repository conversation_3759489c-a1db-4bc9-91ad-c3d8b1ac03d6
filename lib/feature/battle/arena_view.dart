import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:movie_map/core/components/join_battle_button.dart';
import 'package:movie_map/core/helper/basic_helpers.dart';
import 'package:movie_map/product/state/provider/battle_provider.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:movie_map/product/utility/constants/constants.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:vibration/vibration.dart';

class ArenaView extends StatefulWidget {
  const ArenaView({super.key});

  @override
  State<ArenaView> createState() => _ArenaViewState();
}

class _ArenaViewState extends State<ArenaView> {
  List<Map<String, dynamic>> battleList = [];
  String apiKey = ApiConstants.instance.tmdbApiKey;
  bool isLoading = false;
  int currentPage = 0;
  final PageController _pageController = PageController();
  bool isResultAvailable = false;
  bool? isWon;
  bool? isWinnerRight;
  bool? isChoiseRight;

  @override
  void initState() {
    super.initState();
    fetchUnseenBattles();
  }

  Future<void> fetchUnseenBattles() async {
    if (isLoading) return;
    setState(() => isLoading = true);

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // Get user's seen battles
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(currentUser.uid).get();

      List<String> seenBattles = [];
      if (userDoc.exists && userDoc.data()?['seenBattles'] != null) {
        seenBattles = List<String>.from(userDoc.data()!['seenBattles']);
      }
      debugPrint('seenBattles after check: ${seenBattles.length}');

      // Get battles with two opponents that haven't been seen
      final querySnapshot = await FirebaseFirestore.instance.collection('battles').orderBy('createdAt', descending: true).limit(10).get();

      final unseenBattles = querySnapshot.docs.where((doc) {
        // Check if battle has two opponents and hasn't been seen
        final data = doc.data();
        final opponents = data['opponents'] as List?;
        return opponents != null && opponents.length == 2 && !seenBattles.contains(doc.id);
      }).toList();

      debugPrint('unseenBattles: ${unseenBattles.length}');

      for (var doc in unseenBattles) {
        final battle = doc.data();
        final battleId = doc.id;

        // Skip if battle is already in the list
        if (battleList.any((existingBattle) => existingBattle['battleId'] == battleId)) {
          continue;
        }

        List opponents = battle['opponents'];

        // Get user and movie info for both opponents
        DocumentSnapshot opponent1Snapshot = await FirebaseFirestore.instance.collection('users').doc(opponents[0]['userId']).get();
        DocumentSnapshot opponent2Snapshot = await FirebaseFirestore.instance.collection('users').doc(opponents[1]['userId']).get();

        Map<String, dynamic>? movie1 = await fetchMovieFromTMDB(opponents[0]['movieId']);
        Map<String, dynamic>? movie2 = await fetchMovieFromTMDB(opponents[1]['movieId']);

        battleList.add({
          'battleId': battleId,
          'theme': battle['theme'],
          'createdAt': battle['createdAt'],
          'opponent1': {
            'user': opponent1Snapshot.data(),
            'movie': movie1,
            'voteCount': opponents[0]['voteCount'] ?? 0,
          },
          'opponent2': {
            'user': opponent2Snapshot.data(),
            'movie': movie2,
            'voteCount': opponents[1]['voteCount'] ?? 0,
          },
        });
      }

      setState(() => isLoading = false);
    } catch (e) {
      debugPrint('Error fetching unseen battles: $e');
      setState(() => isLoading = false);
    }
  }

  Future<void> fetchMoreBattle() async {}

  Future<Map<String, dynamic>?> fetchMovieFromTMDB(String movieId) async {
    final url = Uri.parse('https://api.themoviedb.org/3/movie/$movieId?api_key=$apiKey&language=en-US');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      debugPrint('TMDB verileri alınamadı. Hata kodu: ${response.statusCode}');
      return null;
    }
  }

  bool winnerFunc(int voteCountLeft, int voteCountRight, String choice) {
    if (choice == 'leftButton') {
      if (voteCountLeft > voteCountRight || voteCountLeft == voteCountRight) {
        isWinnerRight = false;
        return true;
      } else {
        isWinnerRight = true;
        return false;
      }
    } else {
      if (voteCountLeft < voteCountRight || voteCountLeft == voteCountRight) {
        isWinnerRight = true;
        return true;
      } else {
        isWinnerRight = false;
        return false;
      }
    }
  }

  void handleVibration(bool isWon) async {
    if (await Vibration.hasVibrator()) {
      if (isWon) {
        Vibration.vibrate(duration: 600);
      } else {
        Vibration.vibrate(duration: 200);
      }
    }
  }

  bool showCoinAnimation = false;

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        toolbarHeight: 56,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16, top: 16),
            child: Row(
              children: [
                const Text(
                  '💰',
                  style: TextStyle(
                    fontSize: 16,
                  ),
                ),
                const Gap(4),
                Consumer<UserManagerProvider>(
                  builder: (context, provider, child) {
                    final user = provider.userModel;

                    if (user == null) {
                      return const CircularProgressIndicator();
                    }

                    return Text(
                      user.money.toString(),
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    );
                  },
                ),
                const JoinBattleButton()
              ],
            ),
          ),
        ],
      ),
      body: battleList.isEmpty
          ? Center(
              child: isLoading
                  ? const CircularProgressIndicator()
                  : const Text(
                      'No battle available',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            )
          : Stack(
              children: [
                PageView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _pageController,
                  itemCount: battleList.length,
                  onPageChanged: (index) {
                    debugPrint('Battle list length: ${battleList.length}');
                    debugPrint('Current page: $index');
                    debugPrint('You are $index/${battleList.length}');
                    setState(() => currentPage = index);
                    if (index == 4) {
                      fetchUnseenBattles();
                    }
                  },
                  itemBuilder: (context, index) {
                    final battle = battleList[index];
                    final user = battle['opponent1']['user'];
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Gap(screenHeight * 0.08),
                          Text(
                            BasicHelpers().formatDate(battle['createdAt'].toDate()),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            battle['theme'],
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Gap(screenHeight * 0.01),
                          _buildOpponentInfo(
                            index,
                            0,
                            const Color.fromARGB(255, 61, 1, 1),
                          ),
                          Gap(screenHeight * 0.02),
                          _buildOpponentInfo(
                            index,
                            1,
                            const Color.fromARGB(255, 2, 11, 80),
                          ),
                          Gap(screenHeight * 0.01),
                          isWon == null
                              ? commandText('Vote Now!')
                              : isWon!
                                  ? commandText('You are in majority!')
                                  : commandText('Try Again!'),
                          Gap(screenHeight * 0.01),
                          user['username'] == null
                              ? const CircularProgressIndicator()
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CircleVoteComponent(
                                      radiusColor: const Color.fromARGB(255, 49, 1, 1),
                                      username: battle['opponent1']['user']['username'],
                                      opponent: battle['opponent1'],
                                      callback: () async {
                                        // Increment vote
                                        await context.read<BattleProvider>().incrementVote(battle['battleId'].toString(), 0);

                                        // Mark battle as seen
                                        final currentUser = FirebaseAuth.instance.currentUser;
                                        if (currentUser != null) {
                                          await FirebaseFirestore.instance.collection('users').doc(currentUser.uid).update({
                                            'seenBattles': FieldValue.arrayUnion([battle['battleId']])
                                          });
                                        }

                                        // add earn money function.

                                        setState(() {
                                          isResultAvailable = true;
                                          isChoiseRight = false;
                                        });

                                        isWon = winnerFunc(
                                          battle['opponent1']['voteCount'],
                                          battle['opponent2']['voteCount'],
                                          'leftButton',
                                        );

                                        handleVibration(isWon!);

                                        if (isWon!) {
                                          if (currentUser != null) {
                                            // Add 20 coins for winning
                                            await context.read<UserManagerProvider>().addMoney(currentUser.uid, 20);

                                            // Optional: Show success message
                                            if (mounted) {
                                              setState(() {
                                                showCoinAnimation = true;
                                              });
                                            }
                                          }
                                        }

                                        //! TODO: duplication. refactor
                                        Future.delayed(
                                          const Duration(milliseconds: 1000),
                                          () {
                                            if (_pageController.page!.toInt() < battleList.length - 1) {
                                              setState(() {
                                                isChoiseRight = null;
                                                isResultAvailable = false;
                                                isWon = null;
                                                showCoinAnimation = false; // Reset animation state
                                              });
                                              _pageController.nextPage(
                                                duration: const Duration(milliseconds: 300),
                                                curve: Curves.easeIn,
                                              );
                                            }
                                          },
                                        );
                                      },
                                    ),
                                    isResultAvailable
                                        ? Column(
                                            children: [
                                              Padding(
                                                padding: const EdgeInsets.symmetric(horizontal: 6.0),
                                                child: SizedBox(
                                                  width: screenWidth * 0.4,
                                                  child: ClipRRect(
                                                    borderRadius: AppTheme.borderRadiusAll,
                                                    child: RotatedBox(
                                                      quarterTurns: isChoiseRight! ? 2 : 0,
                                                      child: LinearProgressIndicator(
                                                        value: isWinnerRight! && isChoiseRight!
                                                            ? (battle['opponent2']['voteCount'] / (battle['opponent1']['voteCount'] + battle['opponent2']['voteCount']))
                                                            : !isChoiseRight!
                                                                ? (battle['opponent1']['voteCount'] / (battle['opponent1']['voteCount'] + battle['opponent2']['voteCount']))
                                                                : (battle['opponent2']['voteCount'] / (battle['opponent1']['voteCount'] + battle['opponent2']['voteCount'])),
                                                        minHeight: 12,
                                                        backgroundColor: Colors.grey[300],
                                                        valueColor: isWon! ? const AlwaysStoppedAnimation<Color>(Colors.green) : const AlwaysStoppedAnimation<Color>(Colors.red),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: screenWidth * 0.4,
                                                child: Row(
                                                  children: [
                                                    Center(
                                                      child: Text(
                                                        '${((battle['opponent1']['voteCount'] / (battle['opponent1']['voteCount'] + battle['opponent2']['voteCount'])) * 100).toString().substring(0, 2)}%',
                                                        style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                    const Spacer(),
                                                    Center(
                                                      child: Text(
                                                        '${((battle['opponent2']['voteCount'] / (battle['opponent1']['voteCount'] + battle['opponent2']['voteCount'])) * 100).toString().substring(0, 2)}%',
                                                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          )
                                        : const Gap(70),
                                    CircleVoteComponent(
                                      radiusColor: Colors.blue.withOpacity(0.2),
                                      username: battle['opponent2']['user']['username'],
                                      opponent: battle['opponent2'],
                                      callback: () async {
                                        // Increment vote
                                        await context.read<BattleProvider>().incrementVote(battle['battleId'].toString(), 1);

                                        // Mark battle as seen
                                        final currentUser = FirebaseAuth.instance.currentUser;
                                        if (currentUser != null) {
                                          await FirebaseFirestore.instance.collection('users').doc(currentUser.uid).update({
                                            'seenBattles': FieldValue.arrayUnion([battle['battleId']])
                                          });
                                        }

                                        setState(() {
                                          isResultAvailable = true;
                                          isChoiseRight = true;
                                        });

                                        isWon = winnerFunc(
                                          battle['opponent1']['voteCount'],
                                          battle['opponent2']['voteCount'],
                                          'rightButton',
                                        );

                                        handleVibration(isWon!);

                                        if (isWon!) {
                                          if (currentUser != null) {
                                            // Add 20 coins for winning
                                            await context.read<UserManagerProvider>().addMoney(currentUser.uid, 20);

                                            // Optional: Show success message
                                            if (mounted) {
                                              setState(() {
                                                showCoinAnimation = true;
                                              });
                                            }
                                          }
                                        }

                                        //! TODO: duplication. refactor
                                        Future.delayed(
                                          const Duration(milliseconds: 1000),
                                          () {
                                            if (_pageController.page!.toInt() < battleList.length - 1) {
                                              setState(() {
                                                isChoiseRight = null;
                                                isResultAvailable = false;
                                                isWon = null;
                                                showCoinAnimation = false; // Reset animation state
                                              });
                                              _pageController.nextPage(
                                                duration: const Duration(milliseconds: 300),
                                                curve: Curves.easeIn,
                                              );
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ],
                                ),
                        ],
                      ),
                    );
                  },
                ),
                if (showCoinAnimation)
                  CoinAnimation(
                    startX: MediaQuery.of(context).size.width / 2 - 20,
                    startY: MediaQuery.of(context).size.height / 2 - 20,
                    endX: MediaQuery.of(context).size.width * 0.65,
                    endY: -50,
                  ),
              ],
            ),
    );
  }

  Center commandText(String text) {
    return Center(
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildOpponentInfo(
    int battleIndex,
    int opponentIndex,
    Color color,
  ) {
    final battle = battleList[battleIndex];
    final opponent = battle['opponent${opponentIndex + 1}'];
    final user = opponent['user'];
    final movie = opponent['movie'];

    if (user == null || movie == null) {
      return const CircularProgressIndicator();
    }

    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    return Center(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(16),
        ),
        width: screenWidth * 0.96,
        height: screenHeight * 0.27,
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    user['username'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
              ],
            ),
            const Divider(
              thickness: 0.2,
            ),
            const Gap(12),
            opponentIndex == 0
                ? Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                          borderRadius: AppTheme.borderRadiusAll * 1.2,
                          child: Image.network(
                            'https://image.tmdb.org/t/p/w500/${movie['poster_path']}',
                            width: screenWidth * 0.28,
                          ),
                        ),
                        const Gap(12),
                        _MovieTitle(movie: movie),
                      ],
                    ),
                  )
                : Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _MovieTitle(movie: movie),
                        const Spacer(),
                        ClipRRect(
                          borderRadius: AppTheme.borderRadiusAll * 1.2,
                          child: Image.network('https://image.tmdb.org/t/p/w500/${movie['poster_path']}', width: screenWidth * 0.28),
                        ),
                      ],
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}

class _MovieTitle extends StatelessWidget {
  const _MovieTitle({
    required this.movie,
  });

  final dynamic movie;

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth * 0.40,
      child: Text(
        '${movie['title']}',
        textAlign: TextAlign.start,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontSize: 20,
        ),
      ),
    );
  }
}

class CoinAnimation extends StatefulWidget {
  final double startX;
  final double startY;
  final double endX;
  final double endY;

  const CoinAnimation({
    super.key,
    required this.startX,
    required this.startY,
    required this.endX,
    required this.endY,
  });

  @override
  State<CoinAnimation> createState() => _CoinAnimationState();
}

class _CoinAnimationState extends State<CoinAnimation> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..forward().then((_) {
        if (mounted) {
          // Find the parent state and set showCoinAnimation to false
          final arenaState = context.findAncestorStateOfType<_ArenaViewState>();
          if (arenaState != null && arenaState.mounted) {
            arenaState.setState(() {
              arenaState.showCoinAnimation = false;
            });
          }
        }
      });

    _animation = Tween<Offset>(
      begin: Offset(widget.startX, widget.startY),
      end: Offset(widget.endX, widget.endY),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Positioned(
          left: _animation.value.dx,
          top: _animation.value.dy,
          child: const Text(
            '💰',
            style: TextStyle(fontSize: 30),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
