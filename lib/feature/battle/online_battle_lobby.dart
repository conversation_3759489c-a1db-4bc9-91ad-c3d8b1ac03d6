import 'dart:math';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/core/helper/basic_helpers.dart';
import 'package:movie_map/core/helper/costum_button.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/feature/profile/components/movie_tile.dart';
import 'package:movie_map/feature/profile/viewmodel/profile_view_model.dart';
import 'package:movie_map/product/model/battle_model.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/state/provider/battle_provider.dart';
import 'package:provider/provider.dart';

class OnlineBattleLobbyView extends StatefulWidget {
  const OnlineBattleLobbyView({super.key});

  @override
  State<OnlineBattleLobbyView> createState() => _OnlineBattleLobbyViewState();
}

class _OnlineBattleLobbyViewState extends State<OnlineBattleLobbyView> {
  MovieModel? takenMovie;
  int? takenId;
  bool _showComponents = true;
  late String _theme;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // determine theme in initState and set it to _theme
    fetcRandomThemeForBattle();
  }

  Future<void> fetcRandomThemeForBattle() async {
    String randomTheme = '';
    await FirebaseFirestore.instance.collection('battleTheme').get().then((value) {
      final List<String> themes = [];
      for (var element in value.docs) {
        themes.add(element['theme']);
      }

      final random = Random();
      randomTheme = themes[random.nextInt(themes.length)];
    });

    setState(() {
      _theme = randomTheme;
      _isLoading = false;
    });
    debugPrint('Theme: $_theme');
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap(24),
                        const Text(
                          'Choose a movie to battle!',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          BasicHelpers().formatDate(DateTime.now()),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        const Gap(10),
                        Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 6.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.blue.shade700,
                                ),
                                padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8),
                                child: const Text(
                                  'THEME',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                            const Gap(8),
                            Expanded(
                              child: AutoSizeText(
                                _theme,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey.shade200,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const Gap(20),
                        DottedBorder(
                          color: Colors.blue.shade700,
                          strokeWidth: 2,
                          dashPattern: const [12, 4],
                          borderType: BorderType.RRect,
                          radius: const Radius.circular(12),
                          child: Container(
                            height: 150,
                            width: screenWidth * 0.96,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade900,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: takenMovie == null
                                ? Center(
                                    child: Container(
                                      height: 40,
                                      width: 40,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade800,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.add,
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                : Stack(
                                    children: [
                                      MovieTile(movie: takenMovie!),
                                      Positioned(
                                        right: 10,
                                        top: 10,
                                        child: GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              takenMovie = null;
                                              _showComponents = true;
                                            });
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.all(8),
                                            color: Colors.transparent,
                                            child: const Icon(
                                              Icons.close,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                        AnimatedOpacity(
                          opacity: _showComponents ? 1 : 0,
                          duration: const Duration(milliseconds: 750),
                          child: _showComponents
                              ? IconButton(
                                  icon: const Icon(Icons.search),
                                  onPressed: () {
                                    // Search pop up açılır
                                  },
                                )
                              : null,
                        ),
                        Expanded(
                          child: AnimatedOpacity(
                            opacity: _showComponents ? 1 : 0,
                            duration: const Duration(milliseconds: 750),
                            child: _showComponents
                                ? Consumer<MovieCollectionViewModel>(
                                    builder: (context, viewModel, child) {
                                      if (viewModel.movies.isEmpty) {
                                        return const Center(child: Text('No movies in the collection.'));
                                      }

                                      return SingleChildScrollView(
                                        physics: const AlwaysScrollableScrollPhysics(),
                                        child: Column(
                                          children: [
                                            ...viewModel.movies.asMap().entries.map((entry) {
                                              final index = entry.key;
                                              final movie = entry.value;
                                              return GestureDetector(
                                                onTap: () {
                                                  setState(() {
                                                    takenMovie = movie;
                                                    takenId = movie.id;
                                                    _showComponents = false;
                                                  });
                                                },
                                                child: Padding(
                                                  padding: EdgeInsets.only(
                                                    top: index == 0 ? 2.0 : 8.0,
                                                    bottom: 8.0,
                                                  ),
                                                  child: DottedBorder(
                                                    color: Colors.grey.shade700,
                                                    strokeWidth: 2,
                                                    dashPattern: const [12, 4],
                                                    borderType: BorderType.RRect,
                                                    radius: const Radius.circular(12),
                                                    child: Container(
                                                      padding: const EdgeInsets.all(6.0),
                                                      height: 150,
                                                      width: MediaQuery.of(context).size.width * 0.96,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey.shade900,
                                                        borderRadius: BorderRadius.circular(12),
                                                      ),
                                                      child: MovieTile(
                                                        movie: movie,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }),
                                            const Gap(80),
                                          ],
                                        ),
                                      );
                                    },
                                  )
                                : const SizedBox.shrink(),
                          ),
                        )
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12.0),
                      child: CostumButton(
                        onPressed: () async {
                          if (takenId == null) return;
                          final User? user = FirebaseAuth.instance.currentUser;
                          // get theme from database. there is battleTheme collection in firestore
                          // it has different themes. we can get a random theme from there.
                          String randomTheme = '';
                          await FirebaseFirestore.instance.collection('battleTheme').get().then((value) {
                            final List<String> themes = [];
                            for (var element in value.docs) {
                              themes.add(element['theme']);
                            }

                            final random = Random();
                            randomTheme = themes[random.nextInt(themes.length)];
                          });

                          debugPrint('Themelar alındı.');

                          BattleModel newBattle = BattleModel(
                            theme: randomTheme,
                            opponents: [
                              {
                                'userId': user!.uid,
                                'movieId': takenId!.toString(),
                                'voteCount': 1,
                                // TODO: Oy sayısı 0 dan başlamalı ancak uğraşmamak için 1 yaptım. Gelecekte verilerin doğru olması için 0 yapılmalı.
                              },
                            ],
                            createdAt: Timestamp.now(),
                          );

                          debugPrint(takenId!.toString());

                          await context.read<BattleProvider>().createOrJoinBattle(
                                newBattle,
                                user.uid,
                              );

                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => const BottomNavBarPages(),
                            ),
                          );
                        },
                        text: 'Confirm',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
