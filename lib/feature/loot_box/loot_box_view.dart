import 'dart:ui';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/feature/loot_box/loot_box_service.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';

class LootBoxView extends StatefulWidget {
  const LootBoxView({super.key});

  @override
  State<LootBoxView> createState() => _LootBoxViewState();
}

class _LootBoxViewState extends State<LootBoxView> with TickerProviderStateMixin {
  late AnimationController _blurController;
  late Animation<double> _blurAnimation;

  List<MovieModel> lootBoxMovies = [];
  bool isLoading = true;
  bool isBoxOpened = false;
  int currentMovieIndex = 0;
  int totalMoviesCollected = 0;

  // Use the current movie as the cover for the loot box
  MovieModel? get coverMovie => lootBoxMovies.isNotEmpty && currentMovieIndex < lootBoxMovies.length ? lootBoxMovies[currentMovieIndex] : null;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _fetchLootBoxMovies();
  }

  void _initAnimations() {
    _blurController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _blurAnimation = Tween<double>(
      begin: 12.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _blurController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _fetchLootBoxMovies() async {
    try {
      final movies = await LootBoxService().fetchWelcomeLootBoxMovies();
      setState(() {
        lootBoxMovies = movies;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error fetching loot box movies: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _openLootBox() async {
    if (currentMovieIndex >= lootBoxMovies.length) return;

    setState(() {
      isBoxOpened = true;
    });

    // Start blur animation (blur reduces to reveal the movie)
    await _blurController.forward();

    // Show the current movie
    await _showCurrentMovie();
  }

  Future<void> _showCurrentMovie() async {
    if (currentMovieIndex >= lootBoxMovies.length) return;

    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      final userManager = UserManagerProvider();

      // Add current movie to user's collection
      await userManager.addMovieToCollection(
        user.uid,
        lootBoxMovies[currentMovieIndex].id.toString(),
        context,
      );

      totalMoviesCollected++;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '🎬 Got "${lootBoxMovies[currentMovieIndex].title}"! ($totalMoviesCollected/3)',
            style: const TextStyle(fontSize: 16),
          ),
          backgroundColor: AppTheme.main,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          duration: const Duration(seconds: 2),
        ),
      );

      // Wait a bit then prepare for next movie or finish
      await Future.delayed(const Duration(seconds: 2));

      currentMovieIndex++;

      if (currentMovieIndex < lootBoxMovies.length) {
        // Reset for next movie
        setState(() {
          isBoxOpened = false;
        });
        _blurController.reset();
      } else {
        // All movies collected, finish the loot box experience
        await _finishLootBox();
      }
    }
  }

  Future<void> _finishLootBox() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      final userManager = UserManagerProvider();

      // Mark user as having received welcome loot box
      await userManager.markWelcomeLootBoxReceived(user.uid);

      // Show completion message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
            '🎉 Welcome loot box complete! All movies added to your collection!',
            style: TextStyle(fontSize: 16),
          ),
          backgroundColor: AppTheme.lightMain,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          duration: const Duration(seconds: 3),
        ),
      );

      // Wait then navigate to main app
      await Future.delayed(const Duration(seconds: 3));

      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (_) => const BottomNavBarPages(),
        ),
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.background,
              AppTheme.deepMain.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                children: [
                  SizedBox(height: screenHeight * 0.05),

                  // Welcome text
                  const Text(
                    '🎬 Welcome Gift! 🎁',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: screenHeight * 0.02),

                  Text(
                    totalMoviesCollected == 0 ? 'Your starter collection awaits!\nTap the cover to discover 3 amazing movies.' : 'Movie $totalMoviesCollected/3 collected!\n${3 - totalMoviesCollected} more to go!',
                    style: const TextStyle(
                      fontSize: 18,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: screenHeight * 0.05),

                  // Loot box container
                  if (isLoading)
                    const Center(
                      child: CircularProgressIndicator(),
                    )
                  else if (coverMovie != null)
                    _buildLootBoxContainer(screenWidth, screenHeight)
                  else
                    Container(
                      width: screenWidth * 0.7,
                      height: screenWidth * 0.7,
                      decoration: BoxDecoration(
                        color: AppTheme.main.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: const Center(
                        child: Text(
                          'No movies available',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),

                  SizedBox(height: screenHeight * 0.05),

                  SizedBox(height: screenHeight * 0.1),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLootBoxContainer(double screenWidth, double screenHeight) {
    final double imageSize = screenWidth * 0.75;

    return GestureDetector(
      onTap: isBoxOpened ? null : _openLootBox,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Movie cover with blur effect (similar to drop_movie_view)
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: AnimatedBuilder(
                animation: _blurAnimation,
                builder: (context, child) {
                  return ImageFiltered(
                    imageFilter: ImageFilter.blur(
                      sigmaX: _blurAnimation.value,
                      sigmaY: _blurAnimation.value,
                    ),
                    child: SizedBox(
                      height: imageSize,
                      width: imageSize,
                      child: Image.network(
                        'https://image.tmdb.org/t/p/w500${coverMovie!.posterPath}',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppTheme.main.withValues(alpha: 0.3),
                            child: const Center(
                              child: Icon(
                                Icons.movie,
                                size: 100,
                                color: Colors.white,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Blur overlay (similar to drop_movie_view)
          if (!isBoxOpened)
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.grey.shade700.withValues(alpha: 0.85),
                          Colors.grey.shade800.withValues(alpha: 0.95),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.1),
                        width: 0.5,
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _blurController.dispose();
    super.dispose();
  }
}
