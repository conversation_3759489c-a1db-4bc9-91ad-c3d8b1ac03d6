import 'dart:ui';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/core/helper/costum_button.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/feature/loot_box/loot_box_service.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';

class LootBoxView extends StatefulWidget {
  const LootBoxView({super.key});

  @override
  State<LootBoxView> createState() => _LootBoxViewState();
}

class _LootBoxViewState extends State<LootBoxView> with TickerProviderStateMixin {
  late AnimationController _blurController;
  late AnimationController _movieController;
  late Animation<double> _blurAnimation;
  late Animation<double> _movieAnimation;

  List<MovieModel> lootBoxMovies = [];
  bool isLoading = true;
  bool isBoxOpened = false;
  bool isMoviesRevealed = false;
  int currentRevealIndex = 0;

  // Use the first movie as the cover for the loot box
  MovieModel? get coverMovie => lootBoxMovies.isNotEmpty ? lootBoxMovies.first : null;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _fetchLootBoxMovies();
  }

  void _initAnimations() {
    _blurController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _movieController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _blurAnimation = Tween<double>(
      begin: 12.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _blurController,
      curve: Curves.easeOut,
    ));

    _movieAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _movieController,
      curve: Curves.bounceOut,
    ));
  }

  Future<void> _fetchLootBoxMovies() async {
    try {
      final movies = await LootBoxService().fetchWelcomeLootBoxMovies();
      setState(() {
        lootBoxMovies = movies;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error fetching loot box movies: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _openLootBox() async {
    setState(() {
      isBoxOpened = true;
    });

    // Start blur animation (blur reduces to reveal the movie)
    await _blurController.forward();

    // Start revealing movies one by one
    _revealMovies();
  }

  Future<void> _revealMovies() async {
    setState(() {
      isMoviesRevealed = true;
    });

    for (int i = 0; i < lootBoxMovies.length; i++) {
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        currentRevealIndex = i;
      });
      _movieController.reset();
      await _movieController.forward();
    }
  }

  Future<void> _collectAllMovies() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userManager = UserManagerProvider();

        // Add all movies to user's collection
        for (final movie in lootBoxMovies) {
          await userManager.addMovieToCollection(
            user.uid,
            movie.id.toString(),
            context,
          );
        }

        // Mark user as having received welcome loot box
        await userManager.markWelcomeLootBoxReceived(user.uid);

        // Navigate to main app
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (_) => const BottomNavBarPages(),
          ),
          (route) => false,
        );
      }
    } catch (e) {
      debugPrint('Error collecting movies: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error collecting movies: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.background,
              AppTheme.deepMain.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                children: [
                  SizedBox(height: screenHeight * 0.05),

                  // Welcome text
                  const Text(
                    '🎬 Welcome Gift! 🎁',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: screenHeight * 0.02),

                  const Text(
                    'Your starter collection awaits!\nTap the cover to discover 3 amazing movies.',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: screenHeight * 0.05),

                  // Loot box container
                  if (isLoading)
                    const Center(
                      child: CircularProgressIndicator(),
                    )
                  else if (coverMovie != null)
                    _buildLootBoxContainer(screenWidth, screenHeight)
                  else
                    Container(
                      width: screenWidth * 0.7,
                      height: screenWidth * 0.7,
                      decoration: BoxDecoration(
                        color: AppTheme.main.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: const Center(
                        child: Text(
                          'No movies available',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),

                  SizedBox(height: screenHeight * 0.05),

                  // Movies grid
                  if (isMoviesRevealed && lootBoxMovies.isNotEmpty) _buildMoviesGrid(screenWidth),

                  SizedBox(height: screenHeight * 0.03),

                  // Action button
                  if (isMoviesRevealed && currentRevealIndex >= lootBoxMovies.length - 1)
                    CostumButton(
                      onPressed: _collectAllMovies,
                      text: 'Collect All Movies! ✨',
                      gradientColors: [
                        AppTheme.lightMain,
                        AppTheme.main,
                      ],
                    ),

                  SizedBox(height: screenHeight * 0.02),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLootBoxContainer(double screenWidth, double screenHeight) {
    final double imageSize = screenWidth * 0.75;

    return GestureDetector(
      onTap: isBoxOpened ? null : _openLootBox,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Movie cover with blur effect (similar to drop_movie_view)
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: AnimatedBuilder(
                animation: _blurAnimation,
                builder: (context, child) {
                  return ImageFiltered(
                    imageFilter: ImageFilter.blur(
                      sigmaX: _blurAnimation.value,
                      sigmaY: _blurAnimation.value,
                    ),
                    child: SizedBox(
                      height: imageSize,
                      width: imageSize,
                      child: Image.network(
                        'https://image.tmdb.org/t/p/w500${coverMovie!.posterPath}',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppTheme.main.withValues(alpha: 0.3),
                            child: const Center(
                              child: Icon(
                                Icons.movie,
                                size: 100,
                                color: Colors.white,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Blur overlay (similar to drop_movie_view)
          if (!isBoxOpened)
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.grey.shade700.withValues(alpha: 0.85),
                          Colors.grey.shade800.withValues(alpha: 0.95),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.1),
                        width: 0.5,
                      ),
                    ),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.card_giftcard,
                            size: 80,
                            color: Colors.white,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Tap to Open',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMoviesGrid(double screenWidth) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.7,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: lootBoxMovies.length,
      itemBuilder: (context, index) {
        final movie = lootBoxMovies[index];
        final isRevealed = index <= currentRevealIndex;

        return AnimatedBuilder(
          animation: _movieAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: isRevealed ? (0.5 + (_movieAnimation.value * 0.5)) : 0.0,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.main.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Stack(
                    children: [
                      Image.network(
                        'https://image.tmdb.org/t/p/w500${movie.posterPath}',
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      ),

                      // Shine effect
                      if (isRevealed)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white.withValues(alpha: 0.3 * _movieAnimation.value),
                                  Colors.transparent,
                                  Colors.white.withValues(alpha: 0.1 * _movieAnimation.value),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _blurController.dispose();
    _movieController.dispose();
    super.dispose();
  }
}
