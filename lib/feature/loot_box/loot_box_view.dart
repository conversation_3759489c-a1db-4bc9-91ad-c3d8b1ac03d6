import 'dart:ui';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/core/helper/costum_button.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/feature/loot_box/loot_box_service.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';

class LootBoxView extends StatefulWidget {
  const LootBoxView({super.key});

  @override
  State<LootBoxView> createState() => _LootBoxViewState();
}

class _LootBoxViewState extends State<LootBoxView> with TickerProviderStateMixin {
  late AnimationController _boxController;
  late AnimationController _movieController;
  late AnimationController _particleController;
  late AnimationController _pulseController;
  late Animation<double> _boxAnimation;
  late Animation<double> _movieAnimation;
  late Animation<double> _particleAnimation;
  late Animation<double> _pulseAnimation;

  List<MovieModel> lootBoxMovies = [];
  bool isLoading = true;
  bool isBoxOpened = false;
  bool isMoviesRevealed = false;
  int currentRevealIndex = 0;
  bool showParticles = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _fetchLootBoxMovies();
  }

  void _initAnimations() {
    _boxController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _movieController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _boxAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _boxController,
      curve: Curves.elasticOut,
    ));

    _movieAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _movieController,
      curve: Curves.bounceOut,
    ));

    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.easeOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start pulse animation for the loot box
    _pulseController.repeat(reverse: true);
  }

  Future<void> _fetchLootBoxMovies() async {
    try {
      final movies = await LootBoxService().fetchWelcomeLootBoxMovies();
      setState(() {
        lootBoxMovies = movies;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error fetching loot box movies: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _openLootBox() async {
    setState(() {
      isBoxOpened = true;
      showParticles = true;
    });

    // Stop pulse animation
    _pulseController.stop();

    // Start particle effect
    _particleController.forward();

    await _boxController.forward();

    // Start revealing movies one by one
    _revealMovies();
  }

  Future<void> _revealMovies() async {
    setState(() {
      isMoviesRevealed = true;
    });

    for (int i = 0; i < lootBoxMovies.length; i++) {
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        currentRevealIndex = i;
      });
      _movieController.reset();
      await _movieController.forward();
    }
  }

  Future<void> _collectAllMovies() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userManager = UserManagerProvider();

        // Add all movies to user's collection
        for (final movie in lootBoxMovies) {
          await userManager.addMovieToCollection(
            user.uid,
            movie.id.toString(),
            context,
          );
        }

        // Mark user as having received welcome loot box
        await userManager.markWelcomeLootBoxReceived(user.uid);

        // Navigate to main app
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (_) => const BottomNavBarPages(),
          ),
          (route) => false,
        );
      }
    } catch (e) {
      debugPrint('Error collecting movies: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error collecting movies: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.background,
              AppTheme.deepMain.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                children: [
                  SizedBox(height: screenHeight * 0.05),

                  // Welcome text
                  const Text(
                    '🎬 Welcome Gift! 🎁',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: screenHeight * 0.02),

                  const Text(
                    'Your starter collection awaits!\nOpen your loot box to discover 3 amazing movies.',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: screenHeight * 0.05),

                  // Loot box container
                  if (isLoading)
                    const Center(
                      child: CircularProgressIndicator(),
                    )
                  else
                    _buildLootBoxContainer(screenWidth, screenHeight),

                  SizedBox(height: screenHeight * 0.05),

                  // Movies grid
                  if (isMoviesRevealed && lootBoxMovies.isNotEmpty) _buildMoviesGrid(screenWidth),

                  SizedBox(height: screenHeight * 0.03),

                  // Action button
                  if (!isBoxOpened && !isLoading)
                    CostumButton(
                      onPressed: _openLootBox,
                      text: 'Open Loot Box! 🎁',
                      gradientColors: [
                        AppTheme.main,
                        AppTheme.lightMain,
                      ],
                    )
                  else if (isMoviesRevealed && currentRevealIndex >= lootBoxMovies.length - 1)
                    CostumButton(
                      onPressed: _collectAllMovies,
                      text: 'Collect All Movies! ✨',
                      gradientColors: [
                        AppTheme.lightMain,
                        AppTheme.main,
                      ],
                    ),

                  SizedBox(height: screenHeight * 0.02),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLootBoxContainer(double screenWidth, double screenHeight) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Particle effects
        if (showParticles)
          AnimatedBuilder(
            animation: _particleAnimation,
            builder: (context, child) {
              return Container(
                width: screenWidth * 0.9,
                height: screenWidth * 0.9,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.lightMain.withValues(alpha: 0.3 * _particleAnimation.value),
                      Colors.transparent,
                    ],
                  ),
                ),
              );
            },
          ),

        // Main loot box
        AnimatedBuilder(
          animation: isBoxOpened ? _boxAnimation : _pulseAnimation,
          builder: (context, child) {
            final scale = isBoxOpened ? 0.8 + (_boxAnimation.value * 0.2) : _pulseAnimation.value;

            return Transform.scale(
              scale: scale,
              child: Container(
                width: screenWidth * 0.7,
                height: screenWidth * 0.7,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.main.withValues(alpha: 0.8),
                      AppTheme.deepMain,
                      AppTheme.main.withValues(alpha: 0.6),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.main.withValues(alpha: 0.4),
                      blurRadius: 20 + (10 * (isBoxOpened ? _boxAnimation.value : _pulseAnimation.value - 1)),
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Loot box icon
                    Icon(
                      Icons.card_giftcard,
                      size: screenWidth * 0.3,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),

                    // Opening effect
                    if (isBoxOpened)
                      Positioned.fill(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: BackdropFilter(
                            filter: ImageFilter.blur(
                              sigmaX: 5 * _boxAnimation.value,
                              sigmaY: 5 * _boxAnimation.value,
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.white.withValues(alpha: 0.1 * _boxAnimation.value),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildMoviesGrid(double screenWidth) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.7,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: lootBoxMovies.length,
      itemBuilder: (context, index) {
        final movie = lootBoxMovies[index];
        final isRevealed = index <= currentRevealIndex;

        return AnimatedBuilder(
          animation: _movieAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: isRevealed ? (0.5 + (_movieAnimation.value * 0.5)) : 0.0,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.main.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Stack(
                    children: [
                      Image.network(
                        'https://image.tmdb.org/t/p/w500${movie.posterPath}',
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      ),

                      // Shine effect
                      if (isRevealed)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white.withValues(alpha: 0.3 * _movieAnimation.value),
                                  Colors.transparent,
                                  Colors.white.withValues(alpha: 0.1 * _movieAnimation.value),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _boxController.dispose();
    _movieController.dispose();
    _particleController.dispose();
    _pulseController.dispose();
    super.dispose();
  }
}
