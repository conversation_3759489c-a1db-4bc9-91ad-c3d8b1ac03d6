import 'dart:ui';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/core/helper/costum_button.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/service/animation_manager.dart';
import 'package:movie_map/product/service/cache/random_movies_service.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';

class WelcomeLootBoxView extends StatefulWidget {
  final bool isDebugMode;

  const WelcomeLootBoxView({
    super.key,
    this.isDebugMode = false,
  });

  @override
  State<WelcomeLootBoxView> createState() => _WelcomeLootBoxViewState();
}

class _WelcomeLootBoxViewState extends State<WelcomeLootBoxView> with TickerProviderStateMixin {
  final GlobalKey<_FilmStripState> _filmStripKey = GlobalKey<_FilmStripState>();

  late AnimationManager _animationManager;
  late Animation<Offset> _positionAnimation;
  List<String> moviesCoverList = [];
  late MovieModel movie;
  bool isLoading = false;
  int moviesCollected = 0;
  final int totalMovies = 3;

  @override
  void initState() {
    super.initState();
    initAnimation();
    fetchSpinMovies();
  }

  void initAnimation() {
    _animationManager = AnimationManager(vsync: this);
    _positionAnimation = _animationManager.createPositionAnimation(
      duration: const Duration(milliseconds: 800),
      begin: const Offset(-15, -15),
      end: const Offset(0, 0),
    );

    _animationManager.positionController.addStatusListener(
      (status) {
        if (status == AnimationStatus.completed) {
          startPhotoTransition();
        }
      },
    );
  }

  Future<void> fetchSpinMovies() async {
    try {
      movie = await RandomMoviesService().fetchRandomMovie();
      debugPrint('Movie: ${movie.title}');
      moviesCoverList = await RandomMoviesService().fetchRecommendedMovieIds(movie.id);
      debugPrint('$moviesCoverList');
      setState(() {});
    } catch (e) {
      debugPrint('Error fetching recommended movies: $e');
    }
  }

  Future<void> startPositionAnimation() async {
    _animationManager.positionController.forward(from: 0);
  }

  void startPhotoTransition() async {
    const imageDisplayDuration = 300;

    for (int i = 0; i < moviesCoverList.length; i++) {
      Future.delayed(Duration(milliseconds: i * imageDisplayDuration), () {
        if (mounted) {
          if (moviesCoverList.isNotEmpty) {
            moviesCoverList.removeAt(moviesCoverList.length - 1);
            setState(() {});
            debugPrint('Removed: $i');
          }
        }
      });
    }

    _filmStripKey.currentState?.startAnimation();
    _filmStripKey.currentState?._controller?.addStatusListener(
      (status) {
        if (status == AnimationStatus.completed) {
          _collectMovie();
        }
      },
    );
  }

  Future<void> _collectMovie() async {
    moviesCollected++;

    // Add movie to user's collection if not in debug mode
    if (!widget.isDebugMode) {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userManager = UserManagerProvider();
        await userManager.addMovieToCollection(
          user.uid,
          movie.id.toString(),
          context,
        );
      }
    }

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '🎬 Got "${movie.title}"! ($moviesCollected/$totalMovies)',
          style: const TextStyle(fontSize: 16),
        ),
        backgroundColor: AppTheme.main,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: const Duration(seconds: 2),
      ),
    );

    await Future.delayed(const Duration(seconds: 2));

    if (moviesCollected < totalMovies) {
      // Reset for next movie
      await fetchSpinMovies();
      _animationManager.positionController.reset();
      setState(() {});
    } else {
      // All movies collected
      await _finishLootBox();
    }
  }

  Future<void> _finishLootBox() async {
    if (!widget.isDebugMode) {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userManager = UserManagerProvider();
        await userManager.markWelcomeLootBoxReceived(user.uid);
      }
    }

    // Show completion message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(
          '🎉 Welcome gift complete! All movies added to your collection!',
          style: TextStyle(fontSize: 16),
        ),
        backgroundColor: AppTheme.lightMain,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: const Duration(seconds: 3),
      ),
    );

    await Future.delayed(const Duration(seconds: 3));

    if (widget.isDebugMode) {
      Navigator.of(context).pop();
    } else {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (_) => const BottomNavBarPages(),
        ),
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;
    final double screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.main.withValues(alpha: 0.1),
              AppTheme.lightMain.withValues(alpha: 0.2),
              AppTheme.deepMain.withValues(alpha: 0.1),
              AppTheme.main.withValues(alpha: 0.15),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Welcome Gift Title
              const Text(
                '🎁 Welcome Gift! 🎬',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 2),
                      blurRadius: 4,
                      color: Colors.black26,
                    ),
                  ],
                ),
              ),

              const Gap(16),

              Text(
                moviesCollected == 0 ? 'Your starter collection awaits!\nTap to discover amazing movies.' : 'Movie $moviesCollected/$totalMovies collected!\n${totalMovies - moviesCollected} more to go!',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.white70,
                  height: 1.4,
                ),
              ),

              Gap(screenHeight * 0.08),

              // Movie Drop Container (same as drop_movie_view)
              Stack(
                clipBehavior: Clip.none,
                children: [
                  Positioned(
                    top: -screenHeight * 0.14,
                    left: -screenWidth * 0.16,
                    child: FilmStrip(
                      key: _filmStripKey,
                    ),
                  ),
                  AnimatedBuilder(
                    animation: _filmStripKey.currentState?._controller ?? AnimationController(vsync: this),
                    builder: (context, child) {
                      return Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.main.withValues(alpha: 0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(24),
                          child: ImageFiltered(
                            imageFilter: ImageFilter.blur(
                              sigmaX: 12 - (60 * (_filmStripKey.currentState?._controller?.value ?? 0)),
                              sigmaY: 12 - (60 * (_filmStripKey.currentState?._controller?.value ?? 0)),
                            ),
                            child: Opacity(
                              opacity: 1.0,
                              child: SizedBox(
                                height: 300,
                                width: 300,
                                child: Stack(
                                  clipBehavior: Clip.none,
                                  children: moviesCoverList.asMap().entries.map((entry) {
                                    int index = entry.key;
                                    return ClipRRect(
                                      borderRadius: BorderRadius.circular(24),
                                      child: Image.network(
                                        'https://image.tmdb.org/t/p/w500${moviesCoverList[index]}',
                                        height: 300,
                                        width: 300,
                                        fit: BoxFit.cover,
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  AnimatedBuilder(
                    animation: _positionAnimation,
                    builder: (context, child) {
                      return Positioned(
                        bottom: _positionAnimation.value.dy,
                        left: _positionAnimation.value.dx,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(24),
                          child: BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                            child: Container(
                              height: 300,
                              width: 300,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.grey.shade700.withValues(alpha: 0.85),
                                    Colors.grey.shade800.withValues(alpha: 0.95),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(24),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.1),
                                  width: 0.5,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),

              const Gap(60),

              const Gap(120),

              // Open Button
              if (!isLoading)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 24),
                  child: CostumButton(
                    onPressed: () {
                      setState(() {
                        isLoading = true;
                      });
                      startPositionAnimation().then((_) {
                        setState(() {
                          isLoading = false;
                        });
                      });
                    },
                    text: 'Open Welcome Gift! 🎁',
                    gradientColors: [
                      AppTheme.main,
                      AppTheme.lightMain,
                    ],
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                )
              else
                const Center(
                  child: CircularProgressIndicator(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _animationManager.dispose();
    super.dispose();
  }
}

// FilmStrip component (copied from drop_movie_view)
class FilmStrip extends StatefulWidget {
  const FilmStrip({super.key});

  @override
  State<FilmStrip> createState() => _FilmStripState();
}

class _FilmStripState extends State<FilmStrip> with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  Animation<Offset>? _animation;

  @override
  void initState() {
    super.initState();
    initAnimation();
  }

  void initAnimation() {
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    );
    _animation = Tween<Offset>(
      begin: const Offset(0.0, 0),
      end: const Offset(-1, 0),
    ).animate(
      CurvedAnimation(
        parent: _controller!,
        curve: Curves.easeInOutQuart,
      ),
    );
  }

  void startAnimation() {
    _controller?.forward(from: 0);
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double stripHeight = 90;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SlideTransition(
        position: _animation!,
        child: Container(
          height: stripHeight,
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.8),
              width: 3,
            ),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: List.generate(
              30,
              (index) => Container(
                height: stripHeight * 4 / 5,
                width: 100,
                margin: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.6),
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(4),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.1),
                      Colors.white.withValues(alpha: 0.05),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
