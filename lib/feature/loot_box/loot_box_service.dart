import 'dart:math';
import 'package:flutter/material.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/utility/constants/constants.dart';
import 'package:tmdb_api/tmdb_api.dart';

class LootBoxService {
  late TMDB _tmdb;

  LootBoxService() {
    _tmdb = TMDB(
      ApiKeys(ApiConstants.instance.tmdbApiKey, ApiConstants.instance.tmdbApiReadToken),
      logConfig: const ConfigLogger(showLogs: true, showErrorLogs: true),
    );
  }

  /// Fetches 3 random popular movies for the welcome loot box
  Future<List<MovieModel>> fetchWelcomeLootBoxMovies() async {
    try {
      final List<MovieModel> selectedMovies = [];
      final Random random = Random();

      // Fetch popular movies from multiple pages to get variety
      final List<int> pagesToFetch = [1, 2, 3, 4, 5];
      final List<dynamic> allMovies = [];

      for (final page in pagesToFetch) {
        final Map result = await _tmdb.v3.movies.getPopular(page: page);
        final List<dynamic> movies = result['results'] ?? [];
        allMovies.addAll(movies);
      }

      if (allMovies.isEmpty) {
        throw Exception('No movies found');
      }

      // Filter movies to ensure they have required fields
      final List<dynamic> validMovies = allMovies.where((movie) {
        return movie['poster_path'] != null && movie['backdrop_path'] != null && movie['title'] != null && movie['overview'] != null;
      }).toList();

      if (validMovies.length < 3) {
        throw Exception('Not enough valid movies found');
      }

      // Randomly select 3 unique movies
      final Set<int> selectedIndices = <int>{};
      while (selectedIndices.length < 3 && selectedIndices.length < validMovies.length) {
        selectedIndices.add(random.nextInt(validMovies.length));
      }

      for (final index in selectedIndices) {
        final movieData = validMovies[index];
        final movie = MovieModel(
          id: movieData['id'] ?? 0,
          title: movieData['title'] ?? 'Unknown Title',
          overview: movieData['overview'] ?? 'No overview available',
          posterPath: movieData['poster_path'] ?? '',
          backdropPath: movieData['backdrop_path'] ?? '',
          releaseDate: movieData['release_date'] ?? '',
          voteAverage: (movieData['vote_average'] ?? 0.0).toDouble(),
          voteCount: movieData['vote_count'] ?? 0,
          genres: [], // Will be populated later if needed
        );
        selectedMovies.add(movie);
      }

      debugPrint('Successfully fetched ${selectedMovies.length} loot box movies');
      return selectedMovies;
    } catch (e) {
      debugPrint('Error fetching loot box movies: $e');

      // Fallback: return some default movies if API fails
      return _getFallbackMovies();
    }
  }

  /// Fetches movies from specific genres for themed loot boxes
  Future<List<MovieModel>> fetchThemedLootBoxMovies({
    required List<int> genreIds,
    int count = 3,
  }) async {
    try {
      final List<MovieModel> selectedMovies = [];
      final Random random = Random();

      // Discover movies by genre
      final Map result = await _tmdb.v3.discover.getMovies(
        withGenres: genreIds.join(','),
        page: random.nextInt(5) + 1, // Random page between 1-5
      );

      final List<dynamic> movies = result['results'] ?? [];

      if (movies.isEmpty) {
        throw Exception('No movies found for genres: $genreIds');
      }

      // Filter and select movies
      final List<dynamic> validMovies = movies.where((movie) {
        return movie['poster_path'] != null && movie['backdrop_path'] != null && movie['title'] != null;
      }).toList();

      final Set<int> selectedIndices = <int>{};
      final int maxCount = count.clamp(1, validMovies.length);

      while (selectedIndices.length < maxCount) {
        selectedIndices.add(random.nextInt(validMovies.length));
      }

      for (final index in selectedIndices) {
        final movieData = validMovies[index];
        final movie = MovieModel(
          id: movieData['id'] ?? 0,
          title: movieData['title'] ?? 'Unknown Title',
          overview: movieData['overview'] ?? 'No overview available',
          posterPath: movieData['poster_path'] ?? '',
          backdropPath: movieData['backdrop_path'] ?? '',
          releaseDate: movieData['release_date'] ?? '',
          voteAverage: (movieData['vote_average'] ?? 0.0).toDouble(),
          voteCount: movieData['vote_count'] ?? 0,
          genres: [], // Will be populated later if needed
        );
        selectedMovies.add(movie);
      }

      return selectedMovies;
    } catch (e) {
      debugPrint('Error fetching themed loot box movies: $e');
      return _getFallbackMovies();
    }
  }

  /// Returns fallback movies if API calls fail
  List<MovieModel> _getFallbackMovies() {
    return [
      MovieModel(
        id: 550,
        title: 'Fight Club',
        overview: 'A ticking-time-bomb insomniac and a slippery soap salesman channel primal male aggression into a shocking new form of therapy.',
        posterPath: '/pB8BM7pdSp6B6Ih7QZ4DrQ3PmJK.jpg',
        backdropPath: '/hZkgoQYus5vegHoetLkCJzb17zJ.jpg',
        releaseDate: '1999-10-15',
        voteAverage: 8.4,
        voteCount: 26280,
        genres: ['Drama'],
      ),
      MovieModel(
        id: 13,
        title: 'Forrest Gump',
        overview: 'A man with a low IQ has accomplished great things in his life and been present during significant historic events.',
        posterPath: '/arw2vcBveWOVZr6pxd9XTd1TdQa.jpg',
        backdropPath: '/3h1JZGDhZ8nzxdgvkxha0qBqi05.jpg',
        releaseDate: '1994-06-23',
        voteAverage: 8.5,
        voteCount: 24000,
        genres: ['Comedy', 'Drama', 'Romance'],
      ),
      MovieModel(
        id: 680,
        title: 'Pulp Fiction',
        overview: 'A burger-loving hit man, his philosophical partner, a drug-addled gangster\'s moll and a washed-up boxer converge in this sprawling, comedic crime caper.',
        posterPath: '/d5iIlFn5s0ImszYzBPb8JPIfbXD.jpg',
        backdropPath: '/4cDFJr4HnXN5AdPw4AKrmLlMWdO.jpg',
        releaseDate: '1994-09-10',
        voteAverage: 8.5,
        voteCount: 22000,
        genres: ['Crime', 'Drama'],
      ),
    ];
  }

  /// Genre IDs for themed loot boxes
  static const Map<String, List<int>> themeGenres = {
    'action': [28], // Action
    'comedy': [35], // Comedy
    'horror': [27], // Horror
    'romance': [10749], // Romance
    'scifi': [878], // Science Fiction
    'thriller': [53], // Thriller
    'animation': [16], // Animation
    'adventure': [12], // Adventure
  };

  /// Get random theme for loot box
  String getRandomTheme() {
    final themes = themeGenres.keys.toList();
    final random = Random();
    return themes[random.nextInt(themes.length)];
  }

  /// Calculate loot box rarity and rewards
  LootBoxRarity calculateLootBoxRarity() {
    final random = Random();
    final chance = random.nextDouble() * 100;

    if (chance < 60) {
      return LootBoxRarity.common;
    } else if (chance < 85) {
      return LootBoxRarity.rare;
    } else if (chance < 95) {
      return LootBoxRarity.epic;
    } else {
      return LootBoxRarity.legendary;
    }
  }
}

enum LootBoxRarity {
  common,
  rare,
  epic,
  legendary,
}

extension LootBoxRarityExtension on LootBoxRarity {
  String get name {
    switch (this) {
      case LootBoxRarity.common:
        return 'Common';
      case LootBoxRarity.rare:
        return 'Rare';
      case LootBoxRarity.epic:
        return 'Epic';
      case LootBoxRarity.legendary:
        return 'Legendary';
    }
  }

  int get movieCount {
    switch (this) {
      case LootBoxRarity.common:
        return 3;
      case LootBoxRarity.rare:
        return 4;
      case LootBoxRarity.epic:
        return 5;
      case LootBoxRarity.legendary:
        return 6;
    }
  }

  int get bonusCoins {
    switch (this) {
      case LootBoxRarity.common:
        return 50;
      case LootBoxRarity.rare:
        return 100;
      case LootBoxRarity.epic:
        return 200;
      case LootBoxRarity.legendary:
        return 500;
    }
  }
}
