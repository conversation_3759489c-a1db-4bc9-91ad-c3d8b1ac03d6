import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:gap/gap.dart';
import 'package:http/http.dart' as http;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/core/components/join_battle_button.dart';
import 'package:movie_map/feature/battle-history/components/battle_item.dart';
import 'package:movie_map/product/model/battle_model.dart';
import 'package:movie_map/product/utility/constants/constants.dart';

class BattleHistoryView extends StatefulWidget {
  const BattleHistoryView({super.key});

  @override
  State<BattleHistoryView> createState() => _BattleHistoryViewState();
}

class _BattleHistoryViewState extends State<BattleHistoryView> {
  List<BattleModel> battleList = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    fetchUserBattles();
  }

  Future<void> fetchUserBattles() async {
    if (isLoading) return;
    setState(() => isLoading = true);

    try {
      final querySnapshot = await FirebaseFirestore.instance.collection('battles').get();

      final User? user = FirebaseAuth.instance.currentUser;

      final twoOpponentsBattles = querySnapshot.docs.where((doc) {
        final battleData = doc.data();
        final opponents = battleData['opponents'] as List;

        return opponents.length == 2 && (opponents[0]['userId'] == user!.uid || opponents[1]['userId'] == user.uid);
      }).toList();

      for (var doc in twoOpponentsBattles) {
        final battleData = doc.data();
        final battleId = doc.id;

        if (battleList.any((existingBattle) => existingBattle.id == battleId)) {
          continue;
        }

        List opponents = battleData['opponents'];

        DocumentSnapshot opponent1Snapshot = await FirebaseFirestore.instance.collection('users').doc(opponents[0]['userId']).get();
        DocumentSnapshot opponent2Snapshot = await FirebaseFirestore.instance.collection('users').doc(opponents[1]['userId']).get();

        Map<String, dynamic>? movie1 = await fetchMovieFromTMDB(opponents[0]['movieId']);
        Map<String, dynamic>? movie2 = await fetchMovieFromTMDB(opponents[1]['movieId']);

        battleList.add(
          BattleModel(
            id: battleId,
            theme: battleData['theme'],
            createdAt: battleData['createdAt'],
            opponents: [
              {
                'user': opponent1Snapshot.data(),
                'movie': movie1,
                'voteCount': opponents[0]['voteCount'] ?? 0,
              },
              {
                'user': opponent2Snapshot.data(),
                'movie': movie2,
                'voteCount': opponents[1]['voteCount'] ?? 0,
              }
            ],
          ),
        );
      }

      setState(() => isLoading = false);
    } catch (e) {
      debugPrint('Error: $e');
      setState(() => isLoading = false);
    }
  }

  //TODO: Bu sayfa alttan gelsin ve sağ üstünde çarpı iconu olsun.
  // Ancak bu ileride yapılacak bir şey hiç acelesi yok.

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : battleList.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'You have no battles yet.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Gap(20),
                      JoinBattleButton(),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: battleList.length,
                  itemBuilder: (context, index) {
                    final battle = battleList[index];
                    return BattleItem(battle: battle);
                  },
                ),
    );
  }

  String apiKey = ApiConstants.instance.tmdbApiKey;

  Future<Map<String, dynamic>?> fetchMovieFromTMDB(String movieId) async {
    final url = Uri.parse('https://api.themoviedb.org/3/movie/$movieId?api_key=$apiKey&language=en-US');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      debugPrint('TMDB verileri alınamadı. Hata kodu: ${response.statusCode}');
      return null;
    }
  }
}
