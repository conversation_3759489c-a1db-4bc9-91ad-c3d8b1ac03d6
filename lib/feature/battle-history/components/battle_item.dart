import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/product/model/battle_model.dart';

class BattleItem extends StatelessWidget {
  final BattleModel battle;

  const BattleItem({
    super.key,
    required this.battle,
  });

  @override
  Widget build(BuildContext context) {
    final opponent1MoviePoster = battle.opponents[0]['movie']?['poster_path'];
    final opponent2MoviePoster = battle.opponents[1]['movie']?['poster_path'];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.grey.shade900,
        ),
        child: Column(
          children: [
            Row(
              children: [
                Image.network(
                  'https://image.tmdb.org/t/p/w500/$opponent1MoviePoster',
                  width: 80,
                  fit: BoxFit.cover,
                ),
                const Gap(16),
                Image.network(
                  'https://image.tmdb.org/t/p/w500/$opponent2MoviePoster',
                  width: 80,
                  fit: BoxFit.cover,
                ),
                const Gap(12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Gap(8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '${battle.opponents[0]['voteCount']}',
                            style: _textStyle,
                          ),
                          const Gap(8),
                          const Text('⚔️'),
                          const Gap(8),
                          Text(
                            '${battle.opponents[1]['voteCount']}',
                            style: _textStyle,
                          ),
                        ],
                      ),
                      const Gap(8),
                      Text(
                        battle.theme,
                        style: _textStyle.copyWith(
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                      Text(battle.createdAt.toDate().toString().substring(0, 10), style: _textStyle),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  TextStyle get _textStyle => const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      );
}
