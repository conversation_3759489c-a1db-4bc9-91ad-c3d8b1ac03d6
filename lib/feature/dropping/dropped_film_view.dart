import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/state/provider/marker_manager.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:provider/provider.dart';

class DroppedMovieView extends StatelessWidget {
  final MovieModel movie;
  final MarkerId markerId;
  final int price;
  const DroppedMovieView({
    super.key,
    required this.movie,
    required this.markerId,
    required this.price,
  });

  @override
  Widget build(BuildContext context) {
    UserManagerProvider userManagerProvider = context.read<UserManagerProvider>();
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    return SafeArea(
      child: Scaffold(
        body: Stack(
          children: [
            SizedBox(
              width: screenWidth,
              child: ShaderMask(
                shaderCallback: (rect) {
                  return LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      AppTheme.background,
                    ],
                  ).createShader(rect);
                },
                blendMode: BlendMode.darken,
                child: Image.network(
                  'https://image.tmdb.org/t/p/w500${movie.backdropPath}',
                  fit: BoxFit.fitHeight,
                ),
              ),
            ),
            Center(
              child: Column(
                children: [
                  Gap(screenHeight * 0.05),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Image.network(
                      'https://image.tmdb.org/t/p/w500/${movie.posterPath}',
                      width: screenWidth * 0.8,
                    ),
                  ),
                  Gap(screenHeight * 0.02),
                  Container(
                    width: screenWidth * 0.8,
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.main,
                          AppTheme.deepMain,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(28),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.main.withValues(alpha: 0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () async {
                          await userManagerProvider.addMoney(
                            FirebaseAuth.instance.currentUser!.uid,
                            price,
                          );
                          Navigator.pop(context);
                        },
                        borderRadius: BorderRadius.circular(28),
                        child: Center(
                          child: Text(
                            'Sell it $price 💰',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Gap(screenHeight * 0.02),
                  Container(
                    width: screenWidth * 0.8,
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.lightMain,
                          AppTheme.main,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(28),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.lightMain.withValues(alpha: 0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () async {
                          final User? user = FirebaseAuth.instance.currentUser;

                          await userManagerProvider.addMovieToCollection(
                            user!.uid,
                            movie.id.toString(),
                            context,
                          );

                          context.read<MarkerManager>().removeMarker(context, markerId);
                          Navigator.pop(context);
                        },
                        borderRadius: BorderRadius.circular(28),
                        child: const Center(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Icon(
                              //   Icons.movie_creation_outlined,
                              //   color: Colors.white,
                              //   size: 20,
                              // ),
                              // Gap(8),
                              Text(
                                'Take it!',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
