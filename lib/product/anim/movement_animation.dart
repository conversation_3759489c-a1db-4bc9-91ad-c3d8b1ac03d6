import 'package:flutter/material.dart';

class MovementAnimation extends StatefulWidget {
  final double startX;
  final double startY;
  final double endX;
  final double endY;
  final VoidCallback onComplete;

  const MovementAnimation({
    super.key,
    required this.startX,
    required this.startY,
    required this.endX,
    required this.endY,
    required this.onComplete,
  });

  @override
  State<MovementAnimation> createState() => _MovementAnimationState();
}

class _MovementAnimationState extends State<MovementAnimation> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..forward().then((_) {
        if (mounted) {
          widget.onComplete();
        }
      });

    _animation = Tween<Offset>(
      begin: Offset(widget.startX, widget.startY),
      end: Offset(widget.endX, widget.endY),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Positioned(
          left: _animation.value.dx,
          top: _animation.value.dy,
          child: child ?? const Icon(Icons.monetization_on, size: 40, color: Colors.amber),
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
