import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/product/state/provider/genres_provider.dart';
import 'package:movie_map/product/utility/constants/asset_path.dart';
import 'package:provider/provider.dart';

part 'components/interests_category.dart';
part 'components/interest_chip.dart';

// TODO: Bu sayfa kullanılmamaktadır. Kullanıcıların ilgi alan<PERSON> seçebileceği bu sayfa gereksiz görülmüştür.
class MovieGenreSelectionView extends StatefulWidget {
  const MovieGenreSelectionView({super.key});

  @override
  State<MovieGenreSelectionView> createState() => _MovieGenreSelectionViewState();
}

class _MovieGenreSelectionViewState extends State<MovieGenreSelectionView> {
  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final genresProvider = context.watch<GenresProvider>();

    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: screenHeight * 0.32,
                  width: screenWidth,
                  child: Image.asset(
                    AssetPath.onboardImage,
                    fit: BoxFit.fitWidth,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  child: Column(
                    children: [
                      Gap(screenHeight * 0.02),
                      const Row(
                        children: [
                          Text(
                            'Select your \nfavorite genres...',
                            style: TextStyle(
                              fontSize: 36.0,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.left,
                          ),
                          Spacer(),
                        ],
                      ),
                      Gap(screenHeight * 0.06),
                      InterestCategory(
                        interests: genresProvider.genres,
                      ),
                      Gap(screenHeight * 0.06),
                      ElevatedButton(
                        onPressed: () {
                          final selectedGenres = genresProvider.getSelectedGenres();
                          debugPrint(selectedGenres.toString());

                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) => const BottomNavBarPages(),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          fixedSize: Size(screenWidth * 0.8, 54),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: const Text(
                          'Continue ✅',
                          style: TextStyle(fontSize: 24),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 40,
            left: 10,
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
        ],
      ),
    );
  }
}
