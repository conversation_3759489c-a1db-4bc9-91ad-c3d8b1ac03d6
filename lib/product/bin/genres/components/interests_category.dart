part of '../movie_genre_selection_view.dart';

class InterestCategory extends StatelessWidget {
  final List<String> interests;

  const InterestCategory({super.key, required this.interests});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Movie Genres...',
          style: TextStyle(
            fontSize: 24.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Gap(12),
        Wrap(
          spacing: 12.0,
          runSpacing: 4.0,
          children: interests.map((interest) => InterestChip(interest: interest)).toList(),
        ),
      ],
    );
  }
}
