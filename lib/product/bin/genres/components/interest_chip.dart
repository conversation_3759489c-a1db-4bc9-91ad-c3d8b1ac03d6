part of '../movie_genre_selection_view.dart';

class InterestChip extends StatelessWidget {
  final String interest;

  const InterestChip({super.key, required this.interest});

  @override
  Widget build(BuildContext context) {
    final isSelected = context.watch<GenresProvider>().selectedGenres[interest]!;

    return ChoiceChip(
      backgroundColor: Colors.white,
      label: Text(interest),
      selected: isSelected,
      onSelected: (selected) {
        context.read<GenresProvider>().toggleSelection(interest);
      },
      selectedColor: Colors.blue,
    );
  }
}
