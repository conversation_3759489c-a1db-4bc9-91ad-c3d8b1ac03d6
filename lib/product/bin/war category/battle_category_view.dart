// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:movie_map/feature/battle/daily%20battle/battle_view.dart';
// import 'package:movie_map/feature/battle/online%20battle/arena_view.dart';
// import 'package:movie_map/product/theme/theme.dart';

// part 'category_component.dart';

// class ChooseBattleCategoryView extends StatelessWidget {
//   const ChooseBattleCategoryView({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(),
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             _BattleComponent(
//               icon: '📀',
//               title: 'Online Battle',
//               onTap: () {
//                 Navigator.push(
//                   context,
//                   MaterialPageRoute(
//                     builder: (_) => const ArenaView(),
//                   ),
//                 );
//               },
//             ),
//             const Gap(24),
//             _BattleComponent(
//               icon: '⚔️',
//               title: 'Daily Questions',
//               onTap: () {
//                 Navigator.push(
//                   context,
//                   MaterialPageRoute(
//                     builder: (_) => const BattleView(),
//                   ),
//                 );
//               },
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
