// part of 'battle_category_view.dart';

// final class _BattleComponent extends StatelessWidget {
//   const _BattleComponent({
//     required this.title,
//     required this.icon,
//     required this.onTap,
//   });

//   final String title;
//   final String icon;
//   final VoidCallback onTap;

//   @override
//   Widget build(BuildContext context) {
//     final double screenWidth = MediaQuery.of(context).size.width;
//     return GestureDetector(
//       onTap: onTap,
//       child: Container(
//         width: MediaQuery.of(context).size.width * 0.3,
//         height: MediaQuery.of(context).size.width * 0.3,
//         decoration: BoxDecoration(
//           color: Colors.grey,
//           borderRadius: AppTheme.borderRadiusAll,
//           border: Border.all(
//             color: Colors.blue,
//             width: 2,
//           ),
//         ),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Text(
//               icon,
//               style: const TextStyle(
//                 fontSize: 24,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             Gap(screenWidth * 0.03),
//             Text(
//               title,
//               style: const TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
