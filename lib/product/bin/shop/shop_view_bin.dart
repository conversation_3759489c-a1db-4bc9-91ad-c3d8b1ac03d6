// import 'package:dotted_border/dotted_border.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:movie_map/feature/shop/components/product_component.dart';

// class ShopView extends StatelessWidget {
//   const ShopView({super.key});

//   final String _currentBalanceText = 'Your Current Balance';
//   final String _motivationText = 'Get More Coin';
//   final String _inviteChoice = 'or Invite Friends for 💰';
//   final String _legalTexts = 'Terms & Privacy';

//   @override
//   Widget build(BuildContext context) {
//     final double screenHeight = MediaQuery.of(context).size.height;
//     final double screenWidth = MediaQuery.of(context).size.width;
//     return SafeArea(
//       child: Scaffold(
//         appBar: AppBar(),
//         body: SingleChildScrollView(
//           child: Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 8.0),
//             child: Column(
//               children: <Widget>[
//                 const Gap(20),
//                 DottedBorder(
//                   color: Colors.blue.shade700,
//                   strokeWidth: 2,
//                   dashPattern: const [12, 12],
//                   borderType: BorderType.RRect,
//                   radius: const Radius.circular(12),
//                   child: Container(
//                       height: screenHeight * 0.2,
//                       decoration: BoxDecoration(
//                         color: Colors.grey.shade900,
//                         borderRadius: BorderRadius.circular(12),
//                       ),
//                       child: Center(
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             const Text(
//                               '🪙 7,811',
//                               style: TextStyle(
//                                 color: Colors.blue,
//                                 fontSize: 48,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             Text(
//                               _currentBalanceText,
//                               style: const TextStyle(
//                                 color: Colors.blue,
//                                 fontSize: 16,
//                               ),
//                             ),
//                           ],
//                         ),
//                       )),
//                 ),
//                 const Gap(32),
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 24.0),
//                   child: Row(
//                     children: <Widget>[
//                       Expanded(
//                         child: Divider(
//                           thickness: 1,
//                           color: Colors.grey.shade800,
//                         ),
//                       ),
//                       Padding(
//                         padding: const EdgeInsets.symmetric(horizontal: 40.0),
//                         child: Text(
//                           _motivationText,
//                           style: const TextStyle(
//                             color: Colors.grey,
//                           ),
//                         ),
//                       ),
//                       Expanded(
//                         child: Divider(
//                           thickness: 1,
//                           color: Colors.grey.shade800,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 const Gap(32),
//                 const Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     ProductComponent(
//                       price: 67.90,
//                       coin: 1000,
//                       icon: '🪙',
//                     ),
//                     ProductComponent(
//                       price: 194.99,
//                       coin: 2500,
//                       icon: '🪙🪙',
//                     ),
//                     ProductComponent(
//                       isPopular: true,
//                       price: 349.99,
//                       coin: 6000,
//                       icon: '🪙💵',
//                     ),
//                   ],
//                 ),
//                 const Gap(12),
//                 const Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     ProductComponent(
//                       isPopular: true,
//                       price: 789.99,
//                       coin: 15000,
//                       icon: '💵💵',
//                     ),
//                     ProductComponent(
//                       price: 1969.99,
//                       coin: 40000,
//                       icon: '💵💰',
//                     ),
//                     ProductComponent(
//                       price: 3939.99,
//                       coin: 100000,
//                       icon: '💰💰💰',
//                     ),
//                   ],
//                 ),
//                 Gap(screenHeight * 0.05),
//                 ElevatedButton(
//                   onPressed: () async {},
//                   style: ElevatedButton.styleFrom(
//                     fixedSize: Size(screenWidth * 0.8, 60),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(20),
//                     ),
//                     backgroundColor: Colors.yellow.shade700,
//                   ),
//                   child: Text(
//                     _inviteChoice,
//                     style: const TextStyle(
//                       fontSize: 20,
//                       color: Colors.black,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                 ),
//                 const Gap(12),
//                 Text(
//                   _legalTexts,
//                   style: const TextStyle(
//                     color: Colors.grey,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
