import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/product/theme/theme.dart';

class ProductComponent extends StatelessWidget {
  final bool isPopular;
  final double price;
  final int coin;
  final String icon;
  const ProductComponent({
    super.key,
    this.isPopular = false,
    required this.price,
    required this.coin,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;
    double width = MediaQuery.of(context).size.width;
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: width * 0.3,
          decoration: BoxDecoration(
            color: Colors.grey.shade900,
            borderRadius: AppTheme.borderRadiusAll,
          ),
          child: Column(
            children: [
              const Gap(12),
              Text(
                formatCoin(coin),
                style: TextStyle(
                  color: Colors.yellow.shade700,
                  fontSize: 30,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Gap(height * 0.01),
              Text(
                icon,
                style: const TextStyle(
                  fontSize: 36,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.yellow.shade700,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.all(2),
                  child: Center(
                    child: Text(
                      'TRY${price.toString()}',
                      style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (isPopular) ...[
          Positioned(
            top: -24,
            left: 0,
            right: 0,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.purple,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.all(2),
                child: const Center(
                  child: Text(
                    'Most Popular',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  String formatCoin(int coin) {
    if (coin > 1000) {
      return '${(coin / 1000).toStringAsFixed(1).replaceAll('.0', '')}K';
    } else {
      return coin.toString();
    }
  }
}
