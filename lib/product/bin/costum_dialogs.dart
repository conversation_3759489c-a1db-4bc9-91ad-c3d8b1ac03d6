// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:movie_map/core/helper/costum_button.dart';
// import 'package:movie_map/feature/profile/viewmodel/profile_view_model.dart';
// import 'package:movie_map/product/state/provider/marker_manager.dart';
// import 'package:movie_map/product/state/provider/user_manager_provider.dart';
// import 'package:movie_map/product/utility/constants/constants.dart';
// import 'package:provider/provider.dart';

// class CustomDialogs {
//   void showMovieDialogWithBlur(List movies, int i, BuildContext context) => showDialog(
//         context: context,
//         builder: (BuildContext context) {
//           return AlertDialog(
//             title: const Text('You are too far from this movie!'),
//             content: SizedBox(
//               height: 200,
//               child: Column(
//                 children: [
//                   Container(
//                     decoration: BoxDecoration(
//                       image: DecorationImage(
//                         image: NetworkImage('https://image.tmdb.org/t/p/w500${movies[i]['poster_path']}'),
//                         fit: BoxFit.cover,
//                       ),
//                     ),
//                     height: 150,
//                     width: double.infinity,
//                   ),
//                 ],
//               ),
//             ),
//           );
//         },
//       );

//   void showMovieDialog(List movies, int index, BuildContext context) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         const String dropButton = 'Drop Movie 📀';
//         final screenWidth = MediaQuery.of(context).size.width;
//         final screenHeight = MediaQuery.of(context).size.height;
//         return AlertDialog(
//           title: Center(
//             child: Text(
//               movies[index]['title'],
//             ),
//           ),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(10),
//           ),
//           content: SizedBox(
//             height: screenHeight * 0.46,
//             child: Column(
//               children: [
//                 Container(
//                   decoration: BoxDecoration(
//                     image: DecorationImage(
//                       image: NetworkImage('https://image.tmdb.org/t/p/w500${movies[index]['poster_path']}'),
//                     ),
//                   ),
//                   height: 240,
//                 ),
//                 const Gap(10),
//                 Text('${movies[index]['release_date']}'),
//                 const Gap(10),
//                 ElevatedButton(
//                   onPressed: () async {
//                     context.read<UserManagerProvider>().addMovieToCollection(ApiConstants.sampleUserId, movies[index]['id'].toString());

//                     context.read<MovieCollectionViewModel>().fetchUserDataAndMovies(ApiConstants.sampleUserId);

//                     // Remove the marker after adding the movie to the user's collection
//                     context.read<MarkerManager>().removeMarker(MarkerId('marker_$index'));

//                     ScaffoldMessenger.of(context).showSnackBar(
//                       const SnackBar(
//                         content: Text('Movie added to your collection!'),
//                       ),
//                     );
//                     // TODO: Improve UI quality for this stage...

//                     Navigator.pop(context);
//                   },
//                   style: ElevatedButton.styleFrom(
//                     fixedSize: Size(screenWidth * 0.6, 48),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(20),
//                     ),
//                   ),
//                   child: const Text(
//                     dropButton,
//                     style: TextStyle(fontSize: 20),
//                   ),
//                 ),
//                 CostumButton(
//                   onPressed: () {
//                     context.read<UserManagerProvider>().addMovieToCollection(ApiConstants.sampleUserId, movies[index]['id'].toString());

//                     context.read<MovieCollectionViewModel>().fetchUserDataAndMovies(ApiConstants.sampleUserId);

//                     // Remove the marker after adding the movie to the user's collection
//                     context.read<MarkerManager>().removeMarker(MarkerId('marker_$index'));

//                     ScaffoldMessenger.of(context).showSnackBar(
//                       const SnackBar(
//                         content: Text('Movie added to your collection!'),
//                       ),
//                     );
//                     // TODO: Improve UI quality for this stage...

//                     Navigator.pop(context);
//                   },
//                   text: dropButton,
//                 ),
//                 TextButton(
//                   onPressed: () {
//                     context.read<UserManagerProvider>().addMoney(ApiConstants.sampleUserId, 85);

//                     Navigator.pop(context);
//                   },
//                   child: const Text(
//                     'Sell for 💰85',
//                     style: TextStyle(
//                       color: Colors.yellow,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
