import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiConstants {
  // Private constructor
  ApiConstants._privateConstructor();

  // Singleton instance
  static final ApiConstants instance = ApiConstants._privateConstructor();

  // API keys
  final String googleMapsApiKey = dotenv.env['GOOGLE_MAPS_API_KEY']!;
  final String tmdbApiKey = dotenv.env['TMDB_API_KEY']!;
  final String tmdbApiReadToken = dotenv.env['TMDB_API_READ_TOKEN']!;
}
