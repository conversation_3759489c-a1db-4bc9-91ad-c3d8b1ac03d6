import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/product/model/battle_model.dart';
import 'package:movie_map/product/model/user_model.dart';

class BattleProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  BattleModel? _battle;
  BattleModel? get battle => _battle;

  // Get user data
  // Future<void> fetchBattleData(String battleId) async {
  //   try {
  //     DocumentSnapshot doc = await _firestore.collection('battles').doc(battleId).get();
  //     if (doc.exists && doc.data() != null) {
  //       _battle = BattleModel.fromJson(doc.data() as Map<String, dynamic>);
  //       notifyListeners();
  //     }
  //   } catch (e) {
  //     debugPrint('Error fetching battle data: $e');
  //   }
  // }

  Future<void> createOrJoinBattle(BattleModel battle, String currentUserId) async {
    final battlesCollection = FirebaseFirestore.instance.collection('battles');

    try {
      debugPrint("Kontrol: Son 24 saat içinde katılım kontrolü başlıyor.");
      final now = DateTime.now();
      final twentyFourHoursAgo = now.subtract(const Duration(hours: 24));

      final recentBattleQueryStep1 = await battlesCollection.where('opponents', arrayContains: {'userId': currentUserId}).get();

      final recentBattleQuery = recentBattleQueryStep1.docs.where((doc) {
        final data = doc.data();
        final createdAt = data['createdAt'] as Timestamp?;
        return createdAt != null && createdAt.toDate().isAfter(twentyFourHoursAgo);
      }).toList();

      if (recentBattleQuery.isNotEmpty) {
        debugPrint("Her kullanıcı sadece 24 saatte bir savaşa katılabilir.");
        return;
      }

      // Mevcut savaşları getir
      final querySnapshot = await battlesCollection.where('opponents', isNull: false).get();

      for (var doc in querySnapshot.docs) {
        final data = doc.data();
        final opponents = List<Map<String, dynamic>>.from(data['opponents']);

        // Sadece bir rakibi olan savaşı kontrol et
        if (opponents.length == 1) {
          final existingMovieId = opponents[0]['movieId'];

          // Aynı film kimliğiyle savaşa katılım kontrolü
          if (existingMovieId == battle.opponents[0]['movieId']) {
            // Kullanıcının yeni bir savaşa katılması engellendi.
            await battlesCollection.doc(battle.id).set(battle.toJson());
            return;
          }

          // Savaşa katıl
          opponents.add({
            'userId': currentUserId,
            'movieId': battle.opponents[0]['movieId'],
            'voteCount': 1,
          });

          await battlesCollection.doc(doc.id).update({'opponents': opponents});
          debugPrint('User joined an existing battle');
          return;
        }
      }

      // Eğer uygun bir mücadele yoksa, yeni bir mücadele oluştur
      await battlesCollection.doc(battle.id).set(battle.toJson());
      debugPrint('Battle created successfully');
    } catch (e) {
      debugPrint('Error creating or joining battle: $e');
    }
  }

  Future<List<BattleModel>> getUnseenBattles(String userId) async {
    try {
      DocumentSnapshot userDoc = await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists && userDoc.data() != null) {
        List<dynamic>? seenBattles = userDoc['seenBattles'];

        QuerySnapshot battleSnapshot = await _firestore.collection('battles').get();

        final unseenBattles = battleSnapshot.docs
            .where((doc) {
              if (seenBattles == null || seenBattles.isEmpty) {
                return true;
              }
              return !seenBattles.contains(doc.id);
            })
            .map((doc) => BattleModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList();

        return unseenBattles;
      }
    } catch (e) {
      debugPrint('Error fetching unseen battles: $e');
    }

    return [];
  }

  Future<void> incrementVote(String battleId, int opponentIndex) async {
    final battleRef = FirebaseFirestore.instance.collection('battles').doc(battleId);

    // TODO: transaction nedir. Anlayıp gerekliyse dursun yoksa silinebilir.
    await FirebaseFirestore.instance.runTransaction((transaction) async {
      final freshSnapshot = await transaction.get(battleRef);
      // `freshData` için null kontrolü yapılıyor.
      final freshData = freshSnapshot.data();
      if (freshData == null) {
        // freshData null ise burada uygun bir işlem yapabilirsiniz, örneğin bir hata fırlatma veya işlemi sonlandırma.
        throw Exception("Battle not found: $battleId");
      }

      // `opponents` listesini güvenli bir şekilde almak.
      final opponents = List<Map<String, dynamic>>.from(freshData['opponents'] as List);

      // Oy sayısını artırma işlemi.
      opponents[opponentIndex]['voteCount']++;

      // Firestore'daki veriyi güncelleme.
      transaction.update(battleRef, {'opponents': opponents});
    });
  }

  Future<void> markBattleAsSeen(UserModel user, String battleId) async {
    final user = FirebaseAuth.instance.currentUser;
    final firestore = FirebaseFirestore.instance;

    final userDoc = firestore.collection('users').doc(user!.uid);

    // Kullanıcının seenBattles alanına yeni battleId'yi ekle
    await userDoc.update({
      'seenBattles': FieldValue.arrayUnion([battleId]),
    });
  }
}
