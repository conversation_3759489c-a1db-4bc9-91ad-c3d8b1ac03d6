import 'package:flutter/material.dart';

class GenresProvider extends ChangeNotifier {
  final List<String> _genres = [
    'Action',
    'Comedy',
    'Drama',
    'Horror',
    'Romantic',
    'Sci-Fi',
    'Fantasy',
    'Thriller',
    'Documentary',
    'Adventure',
    'Musical',
    'Animation',
  ];

  final Map<String, bool> _selectedGenres = {};

  GenresProvider() {
    // Initialize all genres as unselected
    for (var genre in _genres) {
      _selectedGenres[genre] = false;
    }
  }

  List<String> get genres => _genres;

  Map<String, bool> get selectedGenres => _selectedGenres;

  // Method to toggle selection
  void toggleSelection(String genre) {
    _selectedGenres[genre] = !_selectedGenres[genre]!;
    notifyListeners();
  }

  // Method to get selected genres
  List<String> getSelectedGenres() {
    debugPrint(_selectedGenres.entries.where((entry) => entry.value).map((entry) => entry.key).toList().toString());
    return _selectedGenres.entries.where((entry) => entry.value).map((entry) => entry.key).toList();
  }
}
