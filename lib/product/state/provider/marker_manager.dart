import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:movie_map/feature/dropping/drop_movie_view.dart';
import 'package:movie_map/feature/subscription/subscription_view.dart';
import 'package:movie_map/product/init/init_app.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/state/provider/navigation_provider.dart';
import 'package:movie_map/product/utility/constants/constants.dart';
import 'package:provider/provider.dart';
import 'package:tmdb_api/tmdb_api.dart';

class MarkerManager extends ChangeNotifier {
  Map<MarkerId, Marker> markers = {};
  Set<Circle> circles = {};

  BitmapDescriptor? markerIcon;

  bool isScanAvailable = true;

  // Initialize TMDB API to get movie data
  TMDB moviesList = TMDB(
    ApiKeys(ApiConstants.instance.tmdbApiKey, ApiConstants.instance.tmdbApiReadToken),
    logConfig: const ConfigLogger(showLogs: true, showErrorLogs: true),
  );

  // void getPopularMovies(BuildContext context) async {
  //   DocumentSnapshot userDoc = await FirebaseFirestore.instance.collection('users').doc(ApiConstants.sampleUserId).get();
  //   List<String> movieCollectionIds = List<String>.from(userDoc.get('movieCollection') ?? []);
  //   List movies = await fetchRecommendedMovies(297802, 5);
  //   List filteredMovies = filterMovies(movies, movieCollectionIds);
  //   await scanArea(context, filteredMovies);
  // }

  // List filterMovies(List movies, List<String> movieCollectionIds) {
  //   return movies.where((movie) => !movieCollectionIds.contains(movie['id'].toString())).toList();
  // }

  // TODO: Bu fonksiyonların bir kısmı filmleri gösterirken kullanılacak.
  // Future<List> fetchRecommendedMovies(int movieId, int maxMovies) async {
  //   int currentPage = 1;
  //   int totalPages = 1;
  //   List allMovies = [];

  //   while (currentPage <= totalPages && allMovies.length < maxMovies) {
  //     Map recommendedMovies = await moviesList.v3.movies.getRecommended(movieId, language: 'en', page: currentPage);
  //     totalPages = recommendedMovies['total_pages'];
  //     List movies = recommendedMovies['results'];
  //     allMovies.addAll(movies.length + allMovies.length > maxMovies ? movies.sublist(0, maxMovies - allMovies.length) : movies);
  //     currentPage++;
  //   }
  //   return allMovies;
  // }

  Future<void> fetchMarkersFromFirebase(BuildContext context) async {
    final markerManager = context.read<MarkerManager>();
    QuerySnapshot snapshot = await FirebaseFirestore.instance.collection('users').get();

    for (var doc in snapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;
      if (data['markerLocations'] != null) {
        List<GeoPoint> geoPoints = List<GeoPoint>.from(data['markerLocations']);
        List<LatLng> markerLocations = geoPoints.map((geoPoint) => LatLng(geoPoint.latitude, geoPoint.longitude)).toList();
        markerManager.addMarker(markerLocations, context);
      }
    }
  }

  Future<void> scanArea(BuildContext context) async {
    Position currentLocation = await context.read<NavigationHelper>().determinePosition(context);
    debugPrint('Current location: ${currentLocation.latitude}, ${currentLocation.longitude}');
    LatLng userLocation = LatLng(currentLocation.latitude, currentLocation.longitude);
    List<Marker> newMarkers = await generateMarkers(
      context,
      40,
      userLocation,
      markers.values.toList(),
    );

    List<Marker> markersToSave = newMarkers.where((marker) => !markers.containsKey(marker.markerId)).toList();
    // add to firebase
    addMarkersToFirebase(context, markersToSave);
    // add to current state of markers
    addMarker(newMarkers.map((marker) => marker.position).toList(), context);

    notifyListeners();
  }

  Future<List<Marker>> generateMarkers(
    BuildContext context,
    int numberOfMovies,
    LatLng userLocation,
    List<Marker> existingMarkers,
  ) async {
    const double radius = 1200;
    const double minDistance = 180;
    Random random = Random();
    List<Marker> validMarkers = [];

    // Scan locations' control
    final scanLocations = context.read<UserManagerProvider>().userModel!.scanLocations ?? [];

    // Check if any scan location is within 1200 units of the user's current location
    bool isTooCloseToScanLocation = scanLocations.any((scanPoint) => Geolocator.distanceBetween(scanPoint.latitude, scanPoint.longitude, userLocation.latitude, userLocation.longitude) < radius);

    if (isTooCloseToScanLocation) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You are too close to a previously scanned location.'),
        ),
      );
      return [];
    }

    for (int i = 0; i < numberOfMovies; i++) {
      LatLng newMarkerLocation;
      bool isValidMarker = false;

      int attempts = 0;
      const int maxAttempts = 10;
      do {
        // Generate a random location within the 1200 radius
        newMarkerLocation = generateRandomLocation(userLocation, radius, random);

        // Check if the new marker is too close to existing markers
        bool isDuplicate = existingMarkers.any((marker) => Geolocator.distanceBetween(marker.position.latitude, marker.position.longitude, newMarkerLocation.latitude, newMarkerLocation.longitude) < minDistance);

        if (!isDuplicate) {
          bool isOnRoad = await checkIfOnRoad(newMarkerLocation);
          bool hasSufficientDistance = validateDistanceFromExistingMarkers(
            newMarkerLocation,
            existingMarkers + validMarkers,
            minDistance,
          );

          if (isOnRoad && hasSufficientDistance) {
            isValidMarker = true;
            // debugPrint("Valid marker generated at: $newMarkerLocation");
          } else {
            debugPrint("Marker is not valid: On road: $isOnRoad, Sufficient distance: $hasSufficientDistance");
          }
        }

        attempts++;
      } while (!isValidMarker && attempts < maxAttempts);

      if (isValidMarker) {
        validMarkers.add(createMarker(i, newMarkerLocation, userLocation, context));
      }
    }

    // Add the user's current location to the scanned points
    await context.read<UserManagerProvider>().addScanPoint(GeoPoint(
          userLocation.latitude,
          userLocation.longitude,
        ));

    return validMarkers;
  }

  bool validateDistanceFromExistingMarkers(
    LatLng newMarkerLocation,
    List<Marker> markers,
    double minDistance,
  ) {
    return markers.every((marker) {
      double distance = Geolocator.distanceBetween(
        marker.position.latitude,
        marker.position.longitude,
        newMarkerLocation.latitude,
        newMarkerLocation.longitude,
      );
      return distance >= minDistance;
    });
  }

  Marker createMarker(int index, LatLng location, LatLng userLocation, BuildContext context) {
    return Marker(
      markerId: MarkerId('marker_$index'),
      position: location,
      icon: BitmapDescriptor.defaultMarker,
      onTap: () => handleMarkerTap(userLocation, location, context, MarkerId('marker_$index')),
    );
  }

  void handleMarkerTap(LatLng userLocation, LatLng markerLocation, BuildContext context, MarkerId markerId) {
    double distance = Geolocator.distanceBetween(
      userLocation.latitude,
      userLocation.longitude,
      markerLocation.latitude,
      markerLocation.longitude,
    );

    debugPrint('User location: $userLocation');
    debugPrint('Marker location: $markerLocation');
    debugPrint('Distance to marker: $distance');

    // navigation
    void navigateToDropMovie() {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => DropMovieView(
            markerId: markerId,
          ),
        ),
      );
    }

    if (distance < 200) {
      // you are close to the movie
      navigateToDropMovie();

      debugPrint('yakın');
    } else if (distance > 200 && distance < 600) {
      // you can see the movie
      if (isPremium) {
        navigateToDropMovie();
        debugPrint('premium area');
      } else {
        // premium subscription page
        // TODO: subscription is not working yet
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => const SubscriptionView(),
          ),
        );
      }
    } else {
      // this movie is far away from you
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => DropMovieView(
            markerId: markerId,
            extraDistance: distance - 600,
          ),
        ),
      );
    }
  }

  void addMarkersToFirebase(BuildContext context, List<Marker> markers) {
    List<GeoPoint> geoPoints = markers.map((marker) => GeoPoint(marker.position.latitude, marker.position.longitude)).toList();
    context.read<UserManagerProvider>().addMarkers(geoPoints, context: context);
  }

  void addMarker(List<LatLng> locations, BuildContext context) async {
    for (int i = 0; i < locations.length; i++) {
      LatLng location = locations[i];
      MarkerId markerId = MarkerId('marker_${location.latitude}_${location.longitude}');

      // define user location as LatLng
      Position userPosition = await context.read<NavigationHelper>().determinePosition(context);
      final userLocation = LatLng(userPosition.latitude, userPosition.longitude);

      if (!markers.containsKey(markerId)) {
        Marker marker = Marker(
          markerId: markerId,
          position: location,
          icon: markerIcon!,
          onTap: () => handleMarkerTap(userLocation, location, context, markerId),
        );
        markers[markerId] = marker;
      }
    }
    notifyListeners();
  }

  void removeMarker(BuildContext context, MarkerId markerId) {
    if (markers.containsKey(markerId)) {
      markers.remove(markerId);
      context.read<UserManagerProvider>().removeMarker(markerId);
      notifyListeners();
    }
  }

  LatLng generateRandomLocation(LatLng center, double radius, Random random) {
    double angle = random.nextDouble() * 2 * pi;
    double distance = sqrt(random.nextDouble()) * radius;

    const double earthRadius = 6371000;
    double deltaLat = distance * cos(angle) / earthRadius;
    double deltaLng = distance * sin(angle) / (earthRadius * cos(pi * center.latitude / 180));

    double newLat = center.latitude + deltaLat * 180 / pi;
    double newLng = center.longitude + deltaLng * 180 / pi;

    return LatLng(newLat, newLng);
  }

  Future<bool> checkIfOnRoad(LatLng location) async {
    return true;
  }

  void updateCircles(Set<Circle> newCircles) {
    circles = newCircles;
    notifyListeners();
  }
}
