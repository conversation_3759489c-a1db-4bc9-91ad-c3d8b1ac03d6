import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:movie_map/feature/profile/viewmodel/profile_view_model.dart';
import 'package:movie_map/product/model/user_model.dart';
import 'package:provider/provider.dart';

class UserManagerProvider with ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final User? user = FirebaseAuth.instance.currentUser;

  //We use user model for managing users' data.
  UserModel? _user;
  UserModel? get userModel => _user;

  // Get user data
  Future<void> fetchUserData(String userId) async {
    try {
      DocumentSnapshot doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        _user = UserModel.fromMap(doc.data() as Map<String, dynamic>);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error fetching user data: $e');
    }
  }

  //Add movie
  Future<void> addMovieToCollection(String userId, String movieId, BuildContext context) async {
    try {
      await fetchUserData(userId);
      await _firestore.collection('users').doc(userId).update({
        'movieCollection': FieldValue.arrayUnion([movieId])
      });

      // add to local model and update the UI
      _user!.movieCollection?.add(movieId);
      // use function from MovieCollectionViewModel provider
      context.read<MovieCollectionViewModel>().fetchUserDataAndMovies(userId);
      // notifyListeners();
    } catch (e) {
      debugPrint('Error adding movie to Firebase: $e');
    }
  }

  // Add money
  Future<void> addMoney(String userId, int amount) async {
    try {
      await _firestore.collection('users').doc(userId).update({'money': FieldValue.increment(amount)});

      // Local model update
      _user?.money += amount;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating money in Firebase: $e');
    }
  }

  // spend money
  Future<String?> spendMoney(String userId, int amount) async {
    try {
      // Fetch user's money from Firestore
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists && userDoc.data() != null) {
        final userMoney = userDoc.data()!['money'] as int;

        if (userMoney >= amount) {
          // Update the money in Firestore
          await _firestore.collection('users').doc(userId).update({'money': FieldValue.increment(-amount)});

          _user!.money -= amount;
          notifyListeners();
        } else {
          return 'NotEnough';
        }
      } else {
        return 'UserNotFound';
      }
    } catch (e) {
      debugPrint('Error spending money in Firebase: $e');
      return 'Error';
    }

    return null;
  }

  Future<void> updateUserDescription(String userId, String description) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'profileDescription': description,
      });

      _user?.profileDescription = description;
      notifyListeners();
      debugPrint('User description updated successfully.');
    } catch (e) {
      debugPrint('Failed to update user description: $e');
      rethrow;
    }
  }

  // Add movie to watch later list
  Future<void> addMovieToWatchLater(String userId, String movieId) async {
    if (_user != null && !_user!.watchLater!.contains(movieId)) {
      try {
        await _firestore.collection('users').doc(userId).update({
          'watchLater': FieldValue.arrayUnion([movieId])
        });

        _user!.watchLater?.add(movieId);
        notifyListeners();
      } catch (e) {
        debugPrint('Error adding movie to watch later: $e');
      }
    } else {
      debugPrint('You already have this movie in the watch later list.');
    }
  }

  // Remove movie from watch later list
  Future<void> removeMovieFromWatchLater(String userId, String movieId) async {
    if (_user != null && _user!.watchLater!.contains(movieId)) {
      try {
        await _firestore.collection('users').doc(userId).update({
          'watchLater': FieldValue.arrayRemove([movieId])
        });

        _user!.watchLater?.remove(movieId);
        notifyListeners();
      } catch (e) {
        debugPrint('Error removing movie from watch later: $e');
      }
    } else {
      debugPrint('You don\'t have this movie in the watch later list.');
    }
  }

  // Get markers list as parameter and add marker's locations to markerLocations list on Firebase
  Future<void> addMarkers(List<GeoPoint> markers, {required BuildContext context}) async {
    try {
      await _firestore.collection('users').doc(user!.uid).update({'markerLocations': FieldValue.arrayUnion(markers)});

      _user!.markerLocations?.addAll(markers);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding markers to Firebase: $e');
    }
  }

  // Remove marker from markerLocations list on Firebase
  Future<void> removeMarker(MarkerId marker) async {
    try {
      final GeoPoint geoPoint = markerIdToGeoPoint(marker);
      debugPrint('GeoPoint detected: ${geoPoint.latitude}, ${geoPoint.longitude}');

      await _firestore.collection('users').doc(user!.uid).update({
        'markerLocations': FieldValue.arrayRemove([geoPoint])
      });

      _user!.markerLocations?.removeWhere((location) => location.latitude == geoPoint.latitude && location.longitude == geoPoint.longitude);

      notifyListeners();
    } catch (e) {
      debugPrint('Error removing marker from Firebase: $e');
    }
  }

  GeoPoint markerIdToGeoPoint(MarkerId markerId) {
    // MarkerId formatı: 'marker_latitude_longitude'
    final parts = markerId.value.split('_');
    if (parts.length == 3) {
      final latitude = double.tryParse(parts[1]);
      final longitude = double.tryParse(parts[2]);

      if (latitude != null && longitude != null) {
        return GeoPoint(latitude, longitude);
      }
    }
    throw FormatException('Invalid MarkerId format: ${markerId.value}');
  }

  // add scan point to scanLocations list
  Future<void> addScanPoint(GeoPoint scanPoint) async {
    try {
      await _firestore.collection('users').doc(user!.uid).update({
        'scanLocations': FieldValue.arrayUnion([scanPoint])
      });

      _user!.scanLocations?.add(scanPoint);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding scan point to Firebase: $e');
    }
  }

  // Mark user as having received welcome loot box
  Future<void> markWelcomeLootBoxReceived(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'hasReceivedWelcomeLootBox': true,
      });

      _user?.hasReceivedWelcomeLootBox = true;
      notifyListeners();
      debugPrint('Welcome loot box marked as received for user: $userId');
    } catch (e) {
      debugPrint('Error marking welcome loot box as received: $e');
    }
  }

  // Check if user has received welcome loot box
  Future<bool> hasReceivedWelcomeLootBox(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists && userDoc.data() != null) {
        return userDoc.data()!['hasReceivedWelcomeLootBox'] ?? false;
      }
      return false;
    } catch (e) {
      debugPrint('Error checking welcome loot box status: $e');
      return false;
    }
  }
}
