import 'dart:async';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class NavigationHelper extends ChangeNotifier {
  late GoogleMapController googleMapController;

  final Completer<GoogleMapController> _mapController = Completer<GoogleMapController>();
  get mapController => _mapController;

  Future<void> cameraToPosition(LatLng pos, double zoom, double tilt) async {
    GoogleMapController controller = await _mapController.future;
    CameraPosition newCameraPosition = CameraPosition(
      target: pos,
      zoom: zoom,
      tilt: tilt,
    );
    debugPrint('Camera to position: $pos');
    await controller.animateCamera(CameraUpdate.newCameraPosition(newCameraPosition));

    notifyListeners();
  }

  Future<Position> determinePosition(BuildContext context) async {
    await permissionControl(context);
    Position position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.medium,
    );
    debugPrint('Position in determine position function is: $position');
    return position;
  }

  Future<void> permissionControl(BuildContext context) async {
    late bool isServiceEnabled;
    LocationPermission permission;

    isServiceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!isServiceEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enable location services.'),
        ),
      );
      return;
    }

    // İzin durumunu kontrol et
    permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();

      if (permission == LocationPermission.denied) {
        debugPrint('Location permission are disabled.');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location permission are disabled.'),
          ),
        );
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      debugPrint('Location permission are denied forever.');
      // Go to settings
      await Geolocator.openAppSettings();
      return;
    }
  }
}
