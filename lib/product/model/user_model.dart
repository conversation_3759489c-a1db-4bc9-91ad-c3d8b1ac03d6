import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  String username;
  int money;
  int level;
  int popcornSend;
  String? profileDescription;
  // profile photo from firebase storage
  String? profilePhoto;
  List<String>? movieCollection;
  List<String>? seenBattles;
  List<String>? watchLater;
  List<GeoPoint>? markerLocations;
  List<GeoPoint>? scanLocations;
  List<String>? invitationCodes;

  UserModel({
    required this.username,
    required this.money,
    required this.level,
    required this.popcornSend,
    this.profileDescription,
    this.profilePhoto,
    this.movieCollection,
    this.seenBattles,
    this.watchLater,
    this.markerLocations,
    this.scanLocations,
    this.invitationCodes,
  });

  // Firestore'a kaydetmek için bu modeli bir Map'e dönüştüren fonksiyon
  Map<String, dynamic> toMap() {
    return {
      'username': username,
      'money': money,
      'level': level,
      'popcornSend': popcornSend,
      'profileDescription': profileDescription,
      'movieCollection': movieCollection,
      'seenBattles': seenBattles,
      'watchLater': watchLater,
      'markerLocations': markerLocations,
      'scanLocations': scanLocations,
      'profilePhoto': profilePhoto,
      'invitationCodes': invitationCodes,
    };
  }

  // Firestore'dan veriyi almak için bir fonksiyon
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      username: map['username'],
      money: map['money'],
      level: map['level'],
      popcornSend: map['popcornSend'],
      profileDescription: map['profileDescription'],
      profilePhoto: map['profilePhoto'],
      movieCollection: map['movieCollection'] != null ? List<String>.from(map['movieCollection']) : [],
      seenBattles: map['seenBattles'] != null ? List<String>.from(map['seenBattles']) : [],
      watchLater: map['watchLater'] != null ? List<String>.from(map['watchLater']) : [],
      markerLocations: map['markerLocations'] != null ? List<GeoPoint>.from(map['markerLocations']) : [],
      scanLocations: map['scanLocations'] != null ? List<GeoPoint>.from(map['scanLocations']) : [],
      invitationCodes: map['invitationCodes'] != null ? List<String>.from(map['invitationCodes']) : [],
    );
  }
}
