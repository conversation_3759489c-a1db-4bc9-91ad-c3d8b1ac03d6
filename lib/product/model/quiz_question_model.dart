class QuizQuestion {
  final bool type;
  final String difficulty;
  final String category;
  final String question;
  final bool correctAnswer;
  final List<String> incorrectAnswers;

  QuizQuestion({
    required this.type,
    required this.difficulty,
    required this.category,
    required this.question,
    required this.correctAnswer,
    required this.incorrectAnswers,
  });

  // Factory constructor to create an instance from a JSON object
  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      type: json['type'] == 'boolean',
      difficulty: json['difficulty'],
      category: json['category'],
      question: json['question'],
      correctAnswer: json['correct_answer'] == 'True',
      incorrectAnswers: List<String>.from(json['incorrect_answers']),
    );
  }

  // Method to convert an instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type ? 'boolean' : 'other', // Assuming only boolean for now
      'difficulty': difficulty,
      'category': category,
      'question': question,
      'correct_answer': correctAnswer ? 'True' : 'False',
      'incorrect_answers': incorrectAnswers,
    };
  }
}
