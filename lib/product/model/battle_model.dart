import 'package:cloud_firestore/cloud_firestore.dart';

class BattleModel {
  final String? id;
  final String theme;
  final List<Map<String, dynamic>> opponents;
  final Timestamp createdAt;

  BattleModel({
    this.id,
    required this.theme,
    required this.opponents,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'theme': theme,
      'opponents': opponents,
      'createdAt': createdAt,
    };
  }

  factory BattleModel.fromJson(Map<String, dynamic> json) {
    return BattleModel(
      theme: json['theme'],
      opponents: List<Map<String, dynamic>>.from(
        (json['opponents'] ?? []).map(
          (item) => Map<String, dynamic>.from(item),
        ),
      ),
      createdAt: json['createdAt'],
    );
  }
}
