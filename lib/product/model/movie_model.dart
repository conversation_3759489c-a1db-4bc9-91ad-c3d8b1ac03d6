class MovieModel {
  final int id;
  final String title;
  final String posterPath;
  final String backdropPath;
  final String releaseDate;
  final String overview;
  final double voteAverage;
  final int voteCount;
  final List<String> genres;

  MovieModel({
    required this.id,
    required this.title,
    required this.posterPath,
    required this.backdropPath,
    required this.releaseDate,
    required this.overview,
    required this.voteAverage,
    required this.voteCount,
    required this.genres,
  });

  factory MovieModel.fromMap(Map<dynamic, dynamic> map) {
    return MovieModel(
      id: map['id'],
      title: map['title'],
      posterPath: map['poster_path'],
      backdropPath: map['backdrop_path'],
      releaseDate: map['release_date'],
      overview: map['overview'],
      voteAverage: map['vote_average'],
      voteCount: map['vote_count'],
      genres: map['genres'] != null
          ? List<String>.from(
              map['genres'].map(
                (genre) => genre['name'],
              ),
            )
          : [],
    );
  }
}
