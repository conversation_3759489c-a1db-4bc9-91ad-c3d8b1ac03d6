import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class FormService {
  //TODO: We need to get more information about the user's problem.
  static Future<void> saveFormData(BuildContext context, String topic, String message) async {
    await FirebaseFirestore.instance.collection('forms').add({
      'topic': topic,
      'message': message,
    });
    // show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Form submitted successfully!'),
      ),
    );
  }
}
