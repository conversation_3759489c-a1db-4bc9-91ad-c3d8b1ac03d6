import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/product/model/battle_model.dart';
import 'package:movie_map/product/model/user_model.dart';

class BattleRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  DocumentSnapshot? lastDocument;

  Future<List<BattleModel>> fetchUnseenBattles(int batchSize) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return [];

    final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
    List<String> seenBattles = [];

    if (userDoc.exists && userDoc.data()?['seenBattles'] != null) {
      seenBattles = List<String>.from(userDoc.data()!['seenBattles']);
      debugPrint('seenBattles: $seenBattles');
    }

    Query query = _firestore.collection('battles').orderBy('createdAt', descending: true).limit(batchSize);

    if (lastDocument != null) {
      query = query.startAfterDocument(lastDocument!);
      debugPrint('lastDocument: ${lastDocument!.data()}');
    }

    final querySnapshot = await query.get();

    final battles = querySnapshot.docs
        .where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          final opponents = data['opponents'] as List?;
          return opponents != null && opponents.length == 2 && !seenBattles.contains(doc.id);
        })
        .map((doc) => BattleModel.fromJson(doc.data() as Map<String, dynamic>))
        .toList();

    if (querySnapshot.docs.isNotEmpty) {
      lastDocument = querySnapshot.docs.last;
    }
    debugPrint('${battles.length}');
    return battles;
  }

  Future<UserModel> fetchUser(String userId) async {
    final userDoc = await _firestore.collection('users').doc(userId).get();
    return UserModel.fromMap(userDoc.data() as Map<String, dynamic>);
  }
}
