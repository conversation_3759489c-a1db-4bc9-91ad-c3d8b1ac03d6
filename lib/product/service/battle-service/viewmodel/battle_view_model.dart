import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:movie_map/product/model/battle_model.dart';
import 'package:movie_map/product/model/user_model.dart';
import 'package:movie_map/product/service/battle-service/battle_service.dart';
import 'package:http/http.dart' as http;
import 'package:movie_map/product/utility/constants/constants.dart';

class BattleViewModel extends ChangeNotifier {
  final BattleRepository _repository = BattleRepository();
  List<BattleModel> _battleList = [];
  List<BattleModel> get battleList => _battleList;
  String apiKey = ApiConstants.instance.tmdbApiKey;
  bool isLoading = false;

  Future<void> loadUnseenBattles() async {
    if (isLoading) return;

    isLoading = true;
    notifyListeners();

    try {
      final newBattles = await _repository.fetchUnseenBattles(10);
      _battleList = newBattles;
    } catch (e) {
      debugPrint('Error: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  // fetch user to user model from userId in arena_view.dart
  Future<UserModel> fetchUser(String userId) async {
    return await _repository.fetchUser(userId);
  }

  Future<Map<String, dynamic>?> fetchMovieFromTMDB(String movieId) async {
    final url = Uri.parse('https://api.themoviedb.org/3/movie/$movieId?api_key=$apiKey&language=en-US');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      debugPrint('TMDB verileri alınamadı. Hata kodu: ${response.statusCode}');
      return null;
    }
  }
}
