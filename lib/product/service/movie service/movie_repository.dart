import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/model/user_model.dart';
import 'package:tmdb_api/tmdb_api.dart';

class MovieRepository {
  final TMDB tmdb;
  final FirebaseFirestore firestore;

  MovieRepository(this.tmdb, this.firestore);

  // Fetch the user data from Firestore
  Future<UserModel?> getUserData(String userId) async {
    try {
      DocumentSnapshot doc = await firestore.collection('users').doc(userId).get();

      if (doc.exists) {
        return UserModel.fromMap(doc.data() as Map<String, dynamic>);
      } else {
        print('User not found in Firestore.');
        return null;
      }
    } catch (e) {
      print('Error fetching user data: $e');
      return null;
    }
  }

  Future<List<MovieModel>> getUserMovieCollection(UserModel user) async {
    List<MovieModel> movieCollection = [];

    if (user.movieCollection == null || user.movieCollection!.isEmpty) {
      return movieCollection;
    }

    for (String movieId in user.movieCollection!) {
      try {
        final movieData = await tmdb.v3.movies.getDetails(int.parse(movieId));
        movieCollection.add(MovieModel.fromMap(movieData));
      } catch (e) {
        print('Error fetching movie: $e');
      }
    }

    return movieCollection;
  }
}
