import 'package:flutter/material.dart';

class AnimationManager {
  final TickerProvider vsync;
  AnimationController? _positionController;
  AnimationController? _nextAnimationController;
  Animation<Offset>? _positionAnimation;

  AnimationManager({required this.vsync});

  /// Pozisyon animasyonu oluştur
  Animation<Offset> createPositionAnimation({
    required Duration duration,
    Offset begin = const Offset(0, 0),
    Offset end = const Offset(0, 0),
    Curve curve = Curves.linear,
  }) {
    _positionController = AnimationController(
      vsync: vsync,
      duration: duration,
    );

    _positionAnimation = Tween<Offset>(begin: begin, end: end).animate(
      CurvedAnimation(
        parent: _positionController!,
        curve: curve,
      ),
    );

    return _positionAnimation!;
  }

  /// Pozisyon animasyonu kontrolcüsüne erişim sa<PERSON> getter
  AnimationController get positionController => _positionController!;

  void dispose() {
    _positionController?.dispose();
    _nextAnimationController?.dispose();
  }
}
