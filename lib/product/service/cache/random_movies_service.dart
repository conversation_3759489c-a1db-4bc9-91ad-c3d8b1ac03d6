import 'dart:math';

import 'package:flutter/material.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/utility/constants/constants.dart';
import 'package:tmdb_api/tmdb_api.dart';

class RandomMoviesService {
  final List<int> popularMovieIds = [];
  // final Map<int, List<int>> recommendedMoviesMap = {};

  final TMDB _tmdb = TMDB(
    ApiKeys(ApiConstants.instance.tmdbApiKey, ApiConstants.instance.tmdbApiReadToken),
    logConfig: const ConfigLogger(
      showLogs: true,
      showErrorLogs: true,
    ),
  );

  final String hiveBoxName = 'movieBox';

  // Bu fonksiyon popüler filmleri getirir.
  Future<List<int>> fetchPopularMovieIds() async {
    if (popularMovieIds.isNotEmpty) {
      return popularMovieIds;
    }

    final popularMoviesResponse = await _tmdb.v3.movies.getPopular();
    final results = popularMoviesResponse['results'] as List;

    popularMovieIds.addAll(results.map((movie) => movie['id'] as int));
    debugPrint('Popular movie IDs fetched: $popularMovieIds');
    return popularMovieIds;
  }

  // Bu fonksiyon belirli bir film için önerilen filmleri getirir.
  Future<List<String>> fetchRecommendedMovieIds(int movieId) async {
    // if (recommendedMoviesMap.containsKey(movieId)) {
    //   return recommendedMoviesMap[movieId]!;
    // }

    final recommendationsResponse = await _tmdb.v3.movies.getRecommended(movieId);
    final results = recommendationsResponse['results'] as List;

    //TODO: Kontrol et lazım mı?

    return results.take(10).map((movie) => movie['poster_path'] as String).toList();
  }

  // Fetch a combined list of popular movie IDs and their recommendations
  Future<List<int>> fetchPopularAndRecommendedMovies() async {
    List<int> allMovieIds = [];

    allMovieIds = await fetchPopularMovieIds();

    return allMovieIds;
  }

  Future<MovieModel> fetchRandomMovie() async {
    final random = Random();

    final allMovieLists = await fetchPopularAndRecommendedMovies();

    while (allMovieLists.isNotEmpty) {
      final randomIndex = random.nextInt(allMovieLists.length);

      final randomMovieItem = allMovieLists[randomIndex];

      // convert id to movie model
      //TODO: Burası tekrardan istek atmak zorunda kalmayacak şekilde düzenlenmeli.
      final movieData = await _tmdb.v3.movies.getDetails(randomMovieItem);

      return MovieModel.fromMap(movieData);
    }

    throw Exception('No movies available');
  }

  // Manage hive caches from another file. This file manage only the random movie service.
}
