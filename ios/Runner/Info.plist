<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>GIDClientID</key>
		<string>15298927968-uvrtj3li6s75th56a3hee4pl8puqlju9.apps.googleusercontent.com</string>
		<!-- Put me in the [my_project]/ios/Runner/Info.plist file -->
		<!-- Google Sign-in Section -->
		<key>CFBundleURLTypes</key>
			<array>
				<dict>
					<key>CFBundleTypeRole</key>
					<string>Editor</string>
					<key>CFBundleURLSchemes</key>
					<array>
					<!-- TODO Replace this value: -->
					<!-- Copied from GoogleService-Info.plist key REVERSED_CLIENT_ID -->
					<string>com.googleusercontent.apps.15298927968-uvrtj3li6s75th56a3hee4pl8puqlju9</string>
					</array>
				</dict>
			</array>
		<!-- End of the Google Sign-in Section -->
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Movie Map</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>movie_map</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<!-- Kullanımda konum izini için. -->
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>We need to access your location to add location to your photo.</string>
		
		<key>NSPhotoLibraryUsageDescription</key>
		<string>We need access to your photo library to select a profile picture.</string>
		<key>NSCameraUsageDescription</key>
		<string>We need access to your camera to take a profile picture.</string>
		<key>UIStatusBarHidden</key>
		<false/>
	</dict>
</plist>
